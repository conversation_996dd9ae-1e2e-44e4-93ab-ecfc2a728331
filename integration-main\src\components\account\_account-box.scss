.account-box {
    border-top: 1px solid $color-3--3;
    padding-top: 60px;

    .account-box-item {
        display: flex;
        justify-content: space-between;
        padding: 0;
        text-align: left;

        &::before {
            display: none;
        }

        @include breakpoint(medium down) {
            flex-direction: column;
        }

        &__button-wrapper {
            margin-left: 45px;
            margin-top: 30px;

            @include breakpoint(medium down) {
                margin-left: 0;
            }
        }

        &__title {
            margin-bottom: 20px;

            @include breakpoint(medium down) {
                text-align: center;
            }
        }

        &__description {
            max-width: 612px;
            text-align: left;

            @include breakpoint(medium down) {
                text-align: center;
            }
        }
    }
}

.account-box-item {
    $this: &;

    padding: 0 65px;
    position: relative;
    text-align: center;

    @include breakpoint(medium down) {
        padding-bottom: 90px;
    }

    @include breakpoint(small down) {
        padding: 0 20px 84px;
    }

    &::before {
        @include absolute(65%, 0, null, null);
        @include size(1px, 360px);
        background-color: $color-3--3;
        content: '';
        transform: translateY(-50%);
        z-index: 1;

        @include breakpoint(medium down) {
            @include absolute(unset, 50%, 0, null);
            @include size(calc(100% - 71px), 1px);
            transform: translateX(50%);
        }

        @include breakpoint(small down) {
            width: calc(100% - 50px);
        }
    }

    &__separator {
        @include absolute(146px, 0, null, null);
        @include font(var(--typo-1), 2.2rem, var(--fw-normal));
        @include size(70px);
        align-items: center;
        background-color: $color-white;
        border-radius: 50%;
        box-shadow: 0 3px 6px rgba($color-black, 0.16);
        color: $color-3--4;
        display: flex;
        justify-content: center;
        text-transform: uppercase;
        transform: translateX(50%);
        z-index: 4;

        @include breakpoint(medium down) {
            bottom: 0;
            right: 50%;
            top: auto;
            transform: translateX(50%) translateY(50%);
        }
    }

    &.js-account-scroll-animation {
        @include breakpoint(large only) {
            padding-bottom: 100px;

            #{$this}__separator {
                top: 126px;
            }

            &::before {
                top: calc(50% - 14px);
            }
        }
    }

    &.is-fixed {
        @include breakpoint(large only) {
            @include fixed(50%, null, null, null);
            transform: translateY(-50%);
        }
    }

    &__button-wrapper {
        flex-shrink: 0;
        margin-top: 51px;
    }

    &__title {
        @include font(var(--typo-1), 2.8rem, var(--fw-bold));
        color: var(--color-1--1);
        line-height: 1.25;
        margin-bottom: 10px;

        @include breakpoint(medium down) {
            font-size: 2.6rem;
        }
    }

    &__description {
        @include font(var(--typo-1), 1.6rem, var(--fw-normal));
        color: $color-black;
        text-align: center;
    }

    &__btn {
        @include focus-outline($offset: 1px);
        @include trs();
        background-color: transparent;
        border: 0;
        cursor: pointer;
        display: block;
        margin: 0 auto 7px;
        max-width: 230px;
        padding: 0;

        svg {
            @include size(100%, auto);
            display: block;
            max-height: 60px;
            max-width: 230px;

            path {
                @include trs;
            }
        }

        @include on-event {
            path.background {
                fill: $fc-hover-color;
                stroke: $fc-hover-color;
            }
        }
    }

    &__link {
        @include font(var(--typo-1), 1.4rem, var(--fw-normal));
        color: $fc-link-color;
        display: block;
        margin-top: 8px;
        text-align: center;

        @include on-event {
            text-decoration: none;
        }
    }
}
