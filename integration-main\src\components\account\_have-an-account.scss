.have-an-account {
    align-items: center;
    background-color: var(--color-1--1);
    color: $color-white;
    display: flex;
    justify-content: space-between;
    margin: 0;
    padding: 60px 117px 65px 75px;
    position: relative;
    width: 100%;

    @include breakpoint(medium down) {
        align-items: flex-start;
        flex-direction: column;
        justify-content: flex-start;
        padding: 60px 70px 66px;
    }

    @include breakpoint(small down) {
        padding: 45px 20px;
    }

    &::before {
        @include absolute(0, null, null, 75px);
        @include size(100px, 16px);
        background-color: var(--color-2--1);
        content: '';
        transform: translateY(-50%);
        z-index: 2;

        @include breakpoint(small down) {
            left: 50%;
            transform: translate(-50%, -50%);
        }
    }

    &__info {
        flex-grow: 1;
        margin-right: 45px;
        width: 1%;

        @include breakpoint(medium down) {
            margin: 0 0 35px;
            width: 100%;
        }
    }

    &__title {
        @include font(var(--typo-1), 2.8rem, var(--fw-bold));
        line-height: 2.8rem;
        margin-bottom: 9px;

        @include breakpoint(small down) {
            font-size: 2.4rem;
            text-align: center;
        }
    }

    &__teaser {
        @include font(var(--typo-1), 1.6rem, var(--fw-normal));
        line-height: 2.2rem;

        @include breakpoint(small down) {
            text-align: center;
        }

        a {
            @include on-event() {
                text-decoration: none;
            }
        }
    }

    .btn {
        @include breakpoint(small down) {
            margin: 0 auto;
            padding: 22px 44px;
        }
    }
}
