.comment-item {
    $this: &;

    &__avatar {
        --com-avatar-size: 66px;

        @include size(var(--com-avatar-size));
        align-items: center;
        background-color: var(--color-1--1);
        border-radius: 50%;
        display: flex;
        float: left;
        justify-content: center;
        margin-right: 10px;

        @include breakpoint(small down) {
            @include size(55px);
            margin-right: 5px;
        }

        svg {
            @include size(42px);
            fill: $color-white;

            @include breakpoint(small down) {
                @include size(32px);
            }
        }

        .is-lvl-2 & {
            background-color: var(--color-1--3);

            svg {
                fill: var(--color-1--2);
            }
        }
    }

    &__container {
        overflow: hidden;
    }

    &__wrapper {
        padding: 26px 0 0 26px;
        position: relative;

        @include breakpoint(small down) {
            padding: 20px 0 0 5px;
        }
    }

    &__header {
        @include font(var(--typo-1), 1.2rem);
        color: $color-3--4;
        margin: 0;
        text-transform: uppercase;

        @include breakpoint(small down) {
            font-size: 1.2rem;
        }
    }

    &__reply-link {
        margin-top: 20px;

        p {
            @include font(var(--typo-1), 1.2rem, var(--fw-bold));
            @include icon-before($fa-var-comment-alt);
            color: var(--color-1--2);
            margin: 0;
            text-transform: uppercase;

            a {
                color: var(--color-1--2);
                text-decoration: none;

                @include on-event {
                    text-decoration: underline;
                }
            }

            &::before {
                margin-right: 8px;
            }

            @include breakpoint(small down) {
                font-size: 1.2rem;
            }
        }
    }

    &__author {
        @include font(null, null, var(--fw-bold), normal);

        @include breakpoint(small down) {
            display: block;
        }
    }

    &__content {
        p {
            @include font(var(--typo-1), 1.6rem);
            color: $color-black;
            line-height: 1.5;
            margin: 10px 0 0;

            @include breakpoint(small down) {
                font-size: 1.4rem;
            }

            a {
                color: inherit;

                @include on-event {
                    text-decoration: none;
                }
            }
        }
    }
}
