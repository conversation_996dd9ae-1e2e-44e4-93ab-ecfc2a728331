.contact-item {
    $this: &;

    @extend %link-block-context;
    align-items: flex-start;
    background-color: $color-3--1;
    display: flex;
    padding: 35px;
    width: 100%;

    @include breakpoint(small down) {
        align-items: center;
        flex-direction: column;
    }

    &__picture {
        display: block;
        flex-shrink: 0;
        margin-right: 35px;

        @include breakpoint(medium down) {
            margin-right: 29px;
            max-width: 200px;
        }

        @include breakpoint(small down) {
            margin: 0 auto 20px;
        }
    }

    &__content {
        flex-grow: 1;
        min-width: 1%;

        @include breakpoint(small down) {
            max-width: 480px;
            text-align: center;
        }
    }

    &__theme {
        @include font(var(--typo-1), 1.8rem, var(--fw-normal));
        color: var(--color-1--1);
        display: block;
        letter-spacing: 3.24px;
        margin: 0 0 17px;
        text-transform: uppercase;

        @include breakpoint(medium down) {
            font-size: 1.4rem;
            letter-spacing: 2.52px;
            margin-bottom: 13px;
        }

        @include breakpoint(small down) {
            font-size: 1.2rem;
            letter-spacing: 2.16px;
        }
    }

    &__title {
        @include font(var(--typo-1), 3.5rem, var(--fw-bold));
        color: $color-black;
        line-height: calc(40 / 35);
        margin: 0;

        @include breakpoint(medium down) {
            font-size: 2.2rem;
            line-height: calc(24 / 22);
        }

        @include breakpoint(small down) {
            font-size: 2rem;
            line-height: calc(24 / 20);
        }
    }

    &__title-link {
        @extend %link-block;
        @extend %underline-context;

        .underline {
            @include multiline-underline($size: 2px);
        }
    }

    &__function {
        @include font(var(--typo-1), 1.6rem, var(--fw-normal));
        color: $color-black;
        line-height: calc(22 / 16);
        margin: 0 0 6px;

        &.is-main {
            color: var(--color-1--1);
            font-size: 1.8rem;
            font-weight: var(--fw-bold);
            line-height: calc(22 / 18);
        }
    }

    &__content-info {
        display: flex;
        justify-content: space-between;
        margin-top: 38px;

        @include breakpoint(medium down) {
            flex-direction: column;
            margin-top: 30px;
        }

        @include breakpoint(small down) {
            padding: 0 5px;
        }
    }

    &__details,
    &__infos {
        .infos {
            > *:last-child {
                margin-bottom: 0;
            }

            a {
                display: inline-flex;

                @include fa-icon-style(false) {
                    left: -33px;
                }
            }
        }

        a {
            position: relative;
            z-index: 42;
        }
    }

    &__details {
        padding-right: 15px;

        @include breakpoint(medium down) {
            margin-bottom: 28px;
            padding: 0;
        }
    }

    &__infos {
        align-items: flex-start;
        display: flex;
        flex-direction: column;
        flex-shrink: 0;
        width: 250px;

        @include breakpoint(small down) {
            align-items: center;
            width: 100%;
        }
    }

    &__infos-item {
        &:not(:last-child) {
            margin-bottom: 5px;
        }
    }

    .is-width-66 & {
        @include breakpoint(large) {
            #{$this}__theme {
                font-size: 1.4rem;
                letter-spacing: 2.52px;
                margin-bottom: 13px;

                @include breakpoint(small down) {
                    font-size: 1.2rem;
                    letter-spacing: 2.16px;
                    margin-bottom: 8px;
                }
            }

            #{$this}__title {
                font-size: 2.2rem;
                line-height: calc(24 / 22);
            }

            #{$this}__content-info {
                flex-direction: column;
                margin-top: 30px;
            }

            #{$this}__details {
                margin-bottom: 28px;
            }
        }
    }

    .is-width-33 & {
        @include breakpoint(large) {
            align-items: center;
            flex-direction: column;

            #{$this}__picture {
                margin: 0 auto 20px;
            }

            #{$this}__content {
                max-width: 480px;
                text-align: center;
            }

            #{$this}__theme {
                font-size: 1.4rem;
                letter-spacing: 2.52px;
                margin-bottom: 13px;

                @include breakpoint(small down) {
                    font-size: 1.2rem;
                    letter-spacing: 2.16px;
                    margin-bottom: 8px;
                }
            }

            #{$this}__title {
                font-size: 2rem;
                line-height: calc(24 / 20);
            }

            #{$this}__content-info {
                flex-direction: column;
                margin-top: 30px;
            }

            #{$this}__details {
                margin-bottom: 28px;
            }

            #{$this}__infos {
                align-items: center;
                width: 100%;
            }
        }
    }
}
