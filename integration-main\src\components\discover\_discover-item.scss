.discover-item {
    $this: &;

    display: flex;

    @include breakpoint(small down) {
        flex-direction: column-reverse;
    }

    &.is-image-left {
        @include breakpoint(medium) {
            flex-direction: row-reverse;
        }

        #{$this}__content-wrapper::before {
            @include absolute(50%, calc(100% - 1px), null, auto);
            transform: translateY(-50%) rotate(180deg);

            @include breakpoint(small down) {
                @include absolute(-44px, null, null, 50%);
                transform: translate(-50%, 0) rotate(-90deg);
            }
        }
    }

    .is-full-width & {
        #{$this} {
            &__content-wrapper {
                @include breakpoint(small down) {
                    margin: -35px auto 0;
                    width: calc(100% - 40px);
                }

                &::before {
                    content: none;
                }
            }

            &__content {
                margin: 50px 66px;

                @include breakpoint(small down) {
                    margin: 35px 42px;
                }
            }

            &__category {
                @include breakpoint(large only) {
                    margin-bottom: 20px;
                }
            }

            &__link {
                @include breakpoint(large only) {
                    @include min-size(100px, 63px);
                    font-size: 1.3rem;
                    padding: 1.7em 3.3em;
                }
            }
        }
    }

    &__image {
        width: 50%;

        @include breakpoint(small down) {
            width: 100%;
        }

        img {
            @include object-fit();
            @include size(100%);
        }
    }

    &__content-wrapper {
        align-items: center;
        background-color: $color-3--1;
        display: flex;
        justify-content: center;
        position: relative;
        width: 50%;
        z-index: 2;

        @include breakpoint(small down) {
            width: 100%;
        }

        &::before {
            @include absolute(50%, null, null, calc(100% - 1px));
            @include size(28px, 94px);
            background-color: $color-3--1;
            clip-path: polygon(0 0, 0 100%, 100% 50%);
            content: '';
            transform: translateY(-50%);

            @include breakpoint(medium down) {
                @include size(21px, 68px);
            }

            @include breakpoint(small down) {
                @include absolute(-44px, null, null, 50%);
                transform: translate(-50%, 0) rotate(-90deg);
            }
        }
    }

    &__content {
        margin: 30px 42px;
        max-width: 436px;

        @include breakpoint(small down) {
            margin: 35px 42px;
            text-align: center;
        }
    }

    &__category {
        margin-bottom: 15px;

        @include breakpoint(large only) {
            font-size: 1.8rem;
            letter-spacing: 0;
        }

        @include breakpoint(small down) {
            margin-bottom: 5px;
        }
    }

    &__title {
        font-size: 4.5rem;

        @include breakpoint(medium down) {
            font-size: 2.2rem;
        }

        @include breakpoint(small down) {
            font-size: 2rem;
        }
    }

    & &__link {
        margin-top: 24px;

        @include breakpoint(medium down) {
            margin-top: 20px;
        }
    }
}
