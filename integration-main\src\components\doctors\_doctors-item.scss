.doctors-item {
    @extend %link-block-context;
    align-items: flex-start;
    background-color: $color-3--1;
    display: flex;
    padding: 35px;
    position: relative;

    @include breakpoint(small down) {
        display: block;
        padding: 30px;
    }

    &__picture {
        display: block;
        flex-grow: 0;
        flex-shrink: 0;
        margin: 0 35px 0 0;

        @include breakpoint(medium down) {
            margin: 0 30px 0 0;
            max-width: 200px;
        }

        @include breakpoint(small down) {
            margin: 0 auto 20px;
        }
    }

    &__content {
        padding-top: 20px;

        @include breakpoint(medium down) {
            padding-top: 15px;
        }

        @include breakpoint(small down) {
            padding: 0;
            text-align: center;
        }
    }

    &__name {
        @include font(var(--typo-1), 3.5rem, var(--fw-bold));
        color: $color-black;
        line-height: 4rem;
        margin: 0 0 5px;

        @include breakpoint(medium down) {
            font-size: 2.2rem;
            line-height: 2.4rem;
        }

        @include breakpoint(small down) {
            font-size: 2rem;
        }
    }

    &__name-link {
        @extend %link-block;
        @extend %underline-context;
    }

    &__function {
        @include font(var(--typo-1), 1.8rem, var(--fw-bold));
        color: var(--color-1--1);
        line-height: 2.2rem;
        margin: 0 0 30px;
    }

    &__services {
        @include font(var(--typo-1), 1.6rem, var(--fw-normal));
        color: $color-black;
        line-height: 2.2rem;
        margin: 0;

        strong {
            display: block;
            font-size: 1.8rem;
            font-weight: var(--fw-normal);
            margin: 0 0 5px;
        }
    }
}
