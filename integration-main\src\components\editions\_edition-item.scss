.edition-item {
    @extend %link-block-context;
    display: flex;
    margin-left: -200px;
    width: 835px;

    @include breakpoint(medium down) {
        margin-left: 0;
        padding-top: 8px;
        width: 100%;
    }

    @include breakpoint(small down) {
        align-items: center;
        flex-direction: column;
        text-align: center;
    }

    &__image {
        @include size(281px, 398px);
        display: inline-block;
        flex-shrink: 0;

        @include breakpoint(medium down) {
            @include size(175px, 247px);
        }

        @include breakpoint(small down) {
            @include size(160px, 227px);
            margin: 0 auto;
        }

        img {
            @include object-fit;
            @include size(100%);
            background-color: $color-white;
            box-shadow: 0 0 20px rgba($color-black, 0.16);
        }
    }

    &__content {
        background-color: $color-white;
        display: none;
        flex-grow: 1;
        padding: 0 0 0 80px;
        width: 1%;

        @include breakpoint(medium down) {
            padding: 0 0 0 22px;
        }

        @include breakpoint(small down) {
            padding: 34px 0 0;
            width: 100%;
        }

        .swiper-slide-active & {
            display: block;
        }
    }

    &__category {
        margin-bottom: 13px;
    }

    &__title-link {
        @extend %link-block;
        @extend %underline-context;

        &:focus-visible {
            &::after {
                outline-offset: -3px;
            }
        }
    }

    &__subtitle {
        @include font(null, 3.5rem, var(--fw-normal));
        display: block;
        margin: 5px 0 0;

        @include breakpoint(medium down) {
            @include font(null, 2.5rem, var(--fw-normal));
        }

        @include breakpoint(small down) {
            font-size: 2.2rem;
        }
    }

    &__teaser {
        margin-top: 32px;

        @include breakpoint(medium down) {
            margin-top: 24px;
        }
    }

    &__publication {
        @include font(var(--typo-1), 1.2rem, var(--fw-normal));
        color: $color-3--4;
        display: block;
        margin-top: 32px;
        padding-left: 45px;
        position: relative;
        text-transform: uppercase;

        @include breakpoint(small down) {
            padding-left: 0;
        }

        &::before {
            @include absolute(7px, null, null, 0);
            @include size(35px, 4px);
            background-color: var(--color-2--1);
            content: '';

            @include breakpoint(small down) {
                content: none;
            }
        }
    }

    &__number {
        @include font(null, null, var(--fw-bold), normal);
        display: block;
    }

    &__actions {
        margin-top: 32px;
        position: relative;
        z-index: 3;

        @include breakpoint(small down) {
            display: block;
        }

        .document-actions {
            @include breakpoint(small down) {
                align-items: center;
            }
        }
    }
}
