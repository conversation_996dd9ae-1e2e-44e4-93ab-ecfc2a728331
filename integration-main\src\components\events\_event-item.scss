.event-item {
    $this: &;

    @extend %link-block-context;
    cursor: pointer;
    display: flex;
    flex-direction: column-reverse;
    margin: 0 auto;
    max-width: 384px;
    position: relative;

    @include breakpoint(medium down) {
        align-items: center;
        flex-direction: row-reverse;
        max-width: 100%;
    }

    @include breakpoint(small down) {
        align-items: flex-start;
        flex-direction: column-reverse;
    }

    // styles for HP with background
    .events-home & {
        #{$this} {
            &__content {
                @include breakpoint(large only) {
                    padding: 20px 30px;
                }
            }

            &__title {
                color: $color-white;

                .underline {
                    @include multiline-underline();
                }
            }

            &__category {
                color: var(--color-2--1);
            }

            &__time-place {
                .time-place__item {
                    color: var(--color-1--3);
                }
            }
        }
    }

    // styles for widget/sidebar on CP
    .events-widget & {
        #{$this} {
            &__date {
                @include breakpoint(large only) {
                    @include absolute(0, null, null, 0);
                    margin: 0;
                }
            }

            &__content {
                @include breakpoint(large only) {
                    padding: 30px 20px 10px 0;
                    text-align: center;
                }
            }

            &__time-place {
                .time-place__item {
                    @include breakpoint(large only) {
                        justify-content: center;
                    }
                }
            }
        }
    }

    &__image {
        display: block;

        @include breakpoint(medium down) {
            @include min-size(266px, 177px);
            @include size(266px, 177px);
        }

        @include breakpoint(small down) {
            @include min-size(100px, 67px);
            @include size(100px, 67px);
        }

        img {
            @include object-fit();
            @include size(100%);
        }
    }

    &__wrap {
        display: flex;
        flex-direction: column-reverse;
        width: 100%;
        z-index: 2;

        @include breakpoint(medium only) {
            align-items: center;
            flex-direction: row-reverse;
            justify-content: flex-end;
        }
    }

    &__date {
        margin-top: -40px;
        max-width: 220px;

        @include breakpoint(medium down) {
            margin: 0 0 0 -78px;
            max-width: 169px;
            padding: 12px 20px;
        }

        @include breakpoint(small down) {
            margin: -67px 0 0 100px;
            min-width: calc(100% - 100px);
        }
    }

    &__content {
        padding: 20px 30px 20px 0;

        @include breakpoint(medium down) {
            padding: 8px 8px 8px 30px;
        }

        @include breakpoint(small down) {
            padding: 10px 0;
        }
    }

    &__title {
        line-height: 1.2;
    }

    &__title-link {
        @extend %link-block;
        @extend %underline-context;
    }

    &__category {
        @include font(null, 1.2rem, var(--fw-normal));
        color: var(--color-1--1);
        margin: 5px 0;
    }

    &__time-place {
        margin-top: 15px;

        @include breakpoint(small down) {
            margin-top: 5px;
        }
    }

    &__actions {
        @include absolute(-5px, -5px, null, null);
        z-index: 11;
    }
}
