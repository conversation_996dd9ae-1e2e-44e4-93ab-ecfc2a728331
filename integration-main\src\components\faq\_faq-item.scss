.faq-item {
    $this: &;

    &__toggle {
        @include trs;
        background-color: $color-3--1;
        border: 0;
        color: $color-black;
        cursor: pointer;
        min-height: 135px;
        padding: 35px 130px 35px 35px;
        position: relative;
        text-align: left;
        width: 100%;

        @include on-event {
            background-color: var(--color-1--1);
            color: $color-white;

            #{$this}__toggle-icon,
            #{$this}__toggle-category {
                color: $color-white;
            }
        }

        @include breakpoint(small down) {
            padding: 30px 40px 30px 30px;
        }
    }

    &__toggle-icon {
        @include trs;
        @include absolute(57px, 60px);
        color: var(--color-1--1);
        font-size: 2.2rem;

        @include breakpoint(small down) {
            right: 36px;
            top: 22px;
        }
    }

    &__toggle-category {
        @include trs;
        @include font(null, 1.4rem, var(--fw-normal));
        color: var(--color-1--1);
        display: block;
        letter-spacing: 2.52px;
        margin-bottom: 14px;
        text-transform: uppercase;

        @include breakpoint(small down) {
            font-size: 1.2rem;
            letter-spacing: 2.16px;
            padding-right: 40px;
        }
    }

    &__toggle-text {
        @include font(null, 2.4rem, var(--fw-bold));
        line-height: 1.15;

        @include breakpoint(medium down) {
            font-size: 2.2rem;
        }

        @include breakpoint(small down) {
            font-size: 2rem;
        }
    }

    &__block {
        display: none;
        overflow: hidden;
        z-index: 5;

        &.rte {
            margin: 0;
        }
    }

    &__wrapper {
        background-color: $color-3--1;
        padding: 0 35px 35px 63px;
        position: relative;

        @include breakpoint(small down) {
            padding: 0 30px 30px 50px;
        }

        &::before {
            @include absolute(4px, null, 35px, 35px);
            background-color: var(--color-1--1);
            content: '';
            width: 2px;
        }
    }

    & &__response {
        @include font(null, 2rem, var(--fw-normal));
        line-height: 1.4;
        margin: 0 0 10px;
    }

    & &__text {
        font-size: 1.7rem;
        line-height: 1.5;
        margin: 0 0 15px;

        @include breakpoint(medium down) {
            font-size: 1.6rem;
            line-height: 1.4;
        }

        @include breakpoint(small down) {
            font-size: 1.4rem;
            line-height: 1.3;
        }
    }

    &.is-open {
        #{$this} {
            &__toggle-icon {
                transform: scaleY(-1);
            }

            &__block {
                display: block;
            }
        }
    }
}
