.glossary-item-title {
    @include font(null, 2.8rem, var(--fw-bold));
    background-color: var(--color-1--1);
    color: $color-white;
    display: block;
    margin: 40px 0 10px;
    padding: 12px 34px;
    width: 100%;
}

.glossary-item {
    $this: &;

    @extend %link-block-context;
    background-color: $color-3--1;
    padding: 35px;

    @include breakpoint(small down) {
        padding: 30px;
    }

    &__category {
        margin: 0 0 13px;
    }

    &__title {
        margin: 0 0 20px;
    }

    &__title-link {
        @extend %link-block;
        @extend %underline-context;
    }

    &__teaser {
        &.item-teaser {
            @include breakpoint(small down) {
                font-size: 1.4rem;
                line-height: 1.3;
            }
        }
    }
}
