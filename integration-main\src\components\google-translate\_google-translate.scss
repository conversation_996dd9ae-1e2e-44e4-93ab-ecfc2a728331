.g-translate {
    position: relative;
    z-index: 10;
}

.g-translate-dropdown {
    $this: &;

    @include font(var(--typo-1), 1.2rem, var(--fw-normal));
    @include trs();
    line-height: 1;

    &__toggle {
        @include size(54px, 45px);
        @include trs();
        align-items: center;
        background-color: transparent;
        border: 0;
        color: var(--color-1--1);
        display: flex;
        padding: 10px;
        text-transform: uppercase;

        @include breakpoint(small down) {
            position: relative;
            z-index: 6;
        }

        @include on-event() {
            background-color: var(--color-1--1);
            color: $color-white;
        }

        .header:not(.js-fixed-el) & {
            body:not(.is-mnv-opened) .has-page-image:not(.has-secondary-heading) &,
            body:not(.is-mnv-opened).home-page &,
            body:not(.is-mnv-opened).home-hospital-page & {
                @include breakpoint(large) {
                    color: $color-white;
                }
            }
        }
    }

    &__toggle-flag {
        display: block;
        margin-right: 7px;
        pointer-events: none;
    }

    &__toggle-text {
        @include styles-visually-hidden;
    }

    &__toggle-icon {
        @include trs(transform);
        @include icon-after($fa-var-angle-down);
        font-size: 1.6rem;

        &::after {
            font-size: inherit;
            font-weight: inherit;
        }
    }

    &__block {
        background-color: $color-white;
        left: auto;
        padding: 0;
        right: 0;
    }

    &.is-open {
        #{$this}__toggle {
            background-color: $color-black;
            color: $color-white;
        }

        #{$this}__toggle-icon {
            margin-top: -1px;
            transform: scaleY(-1);
        }

        #{$this}__block {
            box-shadow: 0 8px 10px 0 rgba($color-black, 0.1);
        }
    }
}

.g-translate-langs {
    $this: &;
    display: flex;
    flex-direction: column;

    &__button {
        @include trs;
        background: $color-black;
        border: 0;
        border-radius: 0;
        color: $color-white;
        cursor: pointer;
        min-height: 40px;
        padding: 10px;
        text-align: center;
        text-transform: uppercase;
        width: 100%;

        span {
            pointer-events: none;
        }

        @include on-event {
            background-color: var(--color-1--1);
        }
    }
}

.skiptranslate {
    iframe {
        display: block;
        position: static;
    }
}

.translated-ltr {
    body {
        top: 0 !important;
    }
}
