.heading-filter-toggle {
    @extend %button;
    @extend %button-size-small;
    @extend %button-style-only-icon;
    @extend %button-style-circle;
    @extend %button-style-secondary;

    @include breakpoint(medium) {
        display: none;
    }

    &.is-md-breakpoint {
        @include breakpoint(medium down) {
            display: block;
        }
    }

    &.is-lg-breakpoint {
        display: block;
    }
}

.heading {
    $this: &;

    &.is-center {
        #{$this}__category,
        #{$this}__title,
        #{$this}__teaser,
        #{$this}__name,
        #{$this}__synonyms,
        #{$this}__publication,
        #{$this}__type,
        #{$this}__function {
            text-align: center;
        }

        #{$this}__links,
        #{$this}__title {
            justify-content: center;
            width: 100%;
        }
    }

    &.has-image-left {
        #{$this}__image {
            max-width: 250px;
            order: -1;

            @include breakpoint(medium down) {
                margin-bottom: 25px;
            }

            @include breakpoint(small down) {
                max-width: 100%;
            }
        }

        #{$this}__title-text {
            &::before {
                left: -384px;
                width: 510px;

                @include breakpoint(small down) {
                    left: -427px;
                }
            }
        }

        #{$this}__content {
            @include breakpoint(medium only) {
                width: 1%;
            }
        }
    }

    &__wrapper {
        @extend %container;
        background-color: $color-white;
        display: flex;
        flex-wrap: wrap;
        padding: 18px 118px 40px;

        @include breakpoint(medium down) {
            padding: 18px 62px 40px;
        }

        @include breakpoint(small down) {
            flex-direction: column;
            padding: 20px 20px 40px;
        }
    }

    &__image {
        margin: 0 56px 0 0;
        max-width: 126px;
        z-index: 3;

        @include breakpoint(medium down) {
            margin: 0 34px 0 0;
        }

        @include breakpoint(small down) {
            margin: 0 0 25px;
            max-width: 100%;
        }

        &.is-right {
            margin: 0 0 0 24px;
            max-width: 408px;

            @include breakpoint(medium down) {
                margin: 50px 0 0;
                max-width: 100%;
            }
        }

        &.is-small {
            @include breakpoint(large) {
                max-width: 272px;
            }
        }

        picture {
            display: block;

            &.is-rounded {
                border-radius: 50%;
                overflow: hidden;
            }
        }

        + .heading__content {
            width: 1%;

            @include breakpoint(small down) {
                width: 100%;
            }

            .heading__title-text::before,
            .heading__name-text::before {
                @include breakpoint(large only) {
                    left: -384px;
                    width: 510px;
                }
            }
        }

        #{$this}__status {
            > * {
                display: block;
                margin: 0 0 10px;
            }
        }
    }

    &__image-link {
        @include focus-outline($color: var(--color-1--1), $offset: 2px);
        color: $color-black;
        display: block;
        margin-top: 20px;
        text-align: center;
        width: 100%;

        @include on-event {
            picture {
                //
            }
        }
    }

    &__date {
        margin: 12px 0;
    }

    &__caption {
        @include font(var(--typo-1), 1.6rem, var(--fw-normal));
        color: $color-3--4;
        line-height: 1.35;
        margin-top: 15px;
    }

    &__content {
        align-content: flex-start;
        align-items: flex-start;
        display: flex;
        flex-grow: 2;
        flex-wrap: wrap;
        position: relative;
        width: 1%;

        @include breakpoint(medium down) {
            width: 100%;
        }

        > *:first-child {
            margin-top: 0;
        }

        > *:not(#{$this}__title):not(#{$this}__links) {
            width: 100%;
        }

        &.is-full-width {
            width: 100%;
        }
    }

    &__content-top {
        margin-bottom: 30px;
        width: 100%;

        @include breakpoint(small down) {
            margin-bottom: 20px;
        }
    }

    &__content-bottom {
        width: 100%;

        &.is-elected,
        &.is-works-info,
        &.is-structures,
        &.is-contacts {
            background-color: $color-3--1;
            margin-top: 70px;
            padding: 65px 75px;
            width: 100%;

            @include breakpoint(large only) {
                margin-left: -80px;
                margin-right: -80px;
                width: calc(100% + 160px);
            }

            @include breakpoint(medium down) {
                padding: 40px 55px 25px;
            }

            @include breakpoint(small down) {
                padding: 25px;
            }

            .flex-row {
                > div {
                    align-items: flex-start;
                    display: flex;
                    flex-direction: column;

                    @include breakpoint(small down) {
                        align-items: center;
                    }

                    &:nth-child(1) {
                        @include breakpoint(medium down) {
                            margin: 0 0 40px;
                        }
                    }
                }
            }

            .list-contact {
                margin: -2.5px;

                @include breakpoint(medium down) {
                    display: flex;
                    flex-wrap: wrap;
                    margin-bottom: 15px;
                }

                @include breakpoint(small down) {
                    align-items: center;
                    flex-direction: column;
                }

                &__item {
                    padding: 2.5px;
                }
            }
        }

        &.is-contacts,
        &.is-elected {
            .flex-row {
                > div {
                    &:nth-child(2),
                    &:nth-child(3) {
                        @include breakpoint(medium only) {
                            max-width: 407px;
                        }
                    }
                }
            }
        }

        &.is-works-info {
            .flex-row {
                > div {
                    &:nth-child(1) {
                        margin-bottom: 0;

                        @include breakpoint(small down) {
                            align-items: flex-start;
                            margin: 0 auto;
                            max-width: 430px;
                        }
                    }

                    &:nth-child(2) {
                        @include breakpoint(medium only) {
                            max-width: 250px;
                        }

                        @include breakpoint(small down) {
                            align-items: flex-start;
                            margin: 0 auto;
                            max-width: 430px;
                        }
                    }

                    &:nth-child(3) {
                        @include breakpoint(medium only) {
                            max-width: 355px;
                        }
                    }

                    .list-contact {
                        @include breakpoint(medium down) {
                            margin-top: 10px;
                        }
                    }
                }
            }
        }

        &.is-structures {
            .flex-row {
                @include breakpoint(medium only) {
                    justify-content: space-between;
                }

                > div {
                    &:nth-child(3) {
                        @include breakpoint(medium only) {
                            align-items: center;
                            max-width: 250px;
                        }
                    }
                }
            }
        }
    }

    &__content-buttons {
        width: 100%;
    }

    &__content-btn {
        margin: 25px 5px 10px 0;
    }

    &__category {
        @include font(var(--typo-1), 1.8rem, var(--fw-normal));
        color: var(--color-1--1);
        display: block;
        letter-spacing: 3.24px;
        margin: 0 0 10px;
        text-transform: uppercase;

        @include breakpoint(small down) {
            font-size: 1.4rem;
        }
    }

    &__title,
    &__name {
        @include font(null, 7.5rem, var(--fw-bold));
        align-items: center;
        align-self: stretch;
        color: $color-black;
        flex-grow: 2;
        line-height: 1.25;
        margin: 0 0 5px;

        @include breakpoint(medium down) {
            font-size: 5.5rem;
        }

        @include breakpoint(small down) {
            font-size: 3.8rem;
            line-height: 1.25;
            word-break: break-word;
        }

        + #{$this}__publication {
            margin-bottom: 15px;
            margin-top: 0;
        }
    }

    &__title-text,
    &__name-text {
        @include title-decor($style: primary);
        display: block;
    }

    &__subtitle {
        @include font(null, 3.5rem, var(--fw-normal));

        @include breakpoint(medium down) {
            font-size: 2.5rem;
        }

        @include breakpoint(small down) {
            font-size: 2.2rem;
        }
    }

    &__events-group {
        align-items: center;
        background-color: $color-3--1;
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;

        #{$this}__date {
            margin: 0;

            @include breakpoint(small down) {
                width: 100%;
            }

            .date {
                @include breakpoint(small down) {
                    width: 100%;
                }
            }
        }

        #{$this}__time-place {
            flex: 2;
            margin: 12px 40px;

            @include breakpoint(small down) {
                margin: 12px 25px;
            }
        }

        #{$this}__content-buttons {
            margin: 12px;
            width: auto;
        }
    }

    &__works-group {
        display: flex;

        @include breakpoint(small down) {
            flex-direction: column;
        }
    }

    &__links {
        @extend %button-links-group;
        align-self: stretch;
        flex-shrink: 0;
    }

    &__synonyms {
        @include font(null, 2.4rem, var(--fw-bold));
        color: var(--color-1--1);
        margin: 0 0 5px;
    }

    &__publication {
        margin: 25px 0 0;

        &.is-large {
            margin: 35px 0 5px;

            @include breakpoint(small down) {
                margin: 20px 0 5px;
            }
        }
    }

    &__function {
        @include font(var(--typo-1), 1.6rem, var(--fw-normal));
        color: $color-black;
        line-height: 1.55;
        margin: 0;

        &.is-main {
            @include font(null, 1.8rem, var(--fw-bold));
            color: var(--color-1--1);
            margin: 15px 0 0;

            @include breakpoint(small down) {
                margin: 10px 0 0;
            }
        }
    }

    &__teaser {
        margin-top: 30px;

        @include breakpoint(small down) {
            margin-top: 20px;
        }

        + .heading__status {
            margin-top: 20px;
        }
    }

    &__quote {
        margin-top: 30px;
        position: relative;

        @include breakpoint(small down) {
            margin-top: 20px;
        }

        &::after {
            content: close-quote;
        }

        &::before {
            content: open-quote;
        }
    }

    &__content-state {
        align-items: center;
        border-bottom: 1px solid $color-3--4;
        border-top: 1px solid $color-3--4;
        display: flex;

        @include breakpoint(medium down) {
            align-items: flex-start;
            flex-direction: column;
        }
    }

    &__status {
        margin: 35px 0 0;

        > * {
            display: inline-block;
        }
    }

    .copyright {
        display: block;
    }
}
