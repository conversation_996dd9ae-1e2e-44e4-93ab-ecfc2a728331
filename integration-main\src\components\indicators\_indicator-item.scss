.indicator-item {
    $this: &;

    &__wrapper {
        display: flex;
        flex-wrap: wrap;

        @include breakpoint(medium down, true, true) {
            justify-content: center;
        }
    }

    &__image {
        @include size(250px);
        border-radius: 50%;
        flex-shrink: 0;
        margin: 0 50px 45px 0;
        overflow: hidden;

        @include breakpoint(small down, true, true) {
            margin: 0 auto 30px;
        }
    }

    &__icon {
        align-items: center;
        background: var(--color-1--1);
        display: flex;
        justify-content: center;
        margin: 0 0 30px;
        width: 100%;

        &::after {
            content: '';
            display: block;
            padding-top: 68.8%;
        }

        svg {
            @include size(70px);
            fill: $color-white;
        }
    }

    &__chart {
        flex-shrink: 0;
        margin: 0 24px 50px 0;

        @include breakpoint(medium down, true, true) {
            margin: 0 auto 22px;
            padding: 0 15px;
        }
    }

    &__canvas {
        @include size(384px);

        @include breakpoint(small down, true, true) {
            @include size(282px);
        }
    }

    &__block {
        flex-grow: 1;
        width: 50%;

        @include breakpoint(medium down, true, true) {
            width: 100%;
        }
    }

    &__description {
        h3 {
            color: $color-3--4;
            font-size: 2.2rem;
            font-weight: var(--fw-normal);
            line-height: 1.1;
            margin: 0 0 20px;

            @include breakpoint(medium down, true, true) {
                text-align: center;
            }

            @include breakpoint(small down) {
                font-size: 2rem;
            }
        }

        p {
            font-size: 2rem;
            line-height: 3.4rem;
            margin: 0 0 25px;

            @include breakpoint(medium down, true, true) {
                font-size: 1.8rem;
                text-align: center;
            }
        }

        a {
            text-decoration: underline;

            @include on-event {
                text-decoration: none;
            }
        }
    }

    .chart-data {
        max-width: 500px;

        @include breakpoint(medium only) {
            padding-left: 81px;
        }

        &__item {
            padding-left: 40px;
        }

        &__colorbox {
            @include size(30px);
            border-radius: 50%;
            top: calc(50%);
            transform: translateY(-50%);
        }
    }

    // Styles for vertical indicator-item
    &.is-column {
        #{$this}__chart {
            margin: 0 0 20px;
            width: 100%;

            @include breakpoint(medium down) {
                width: auto;
            }
        }

        #{$this}__canvas {
            @include size(282px);
            margin: 0;
        }
    }

    // Styles for boxed indicator-item
    &.is-box {
        #{$this}__block {
            @include breakpoint(small down) {
                margin: 0;
            }
        }

        #{$this}__description {
            margin: 0 auto;
            width: 80%;

            @include breakpoint(medium only) {
                width: 63%;
            }

            h3 {
                margin: 0 0 20px;
                text-align: center;
            }

            p {
                margin: 0 0 10px;
            }
        }
    }
}
