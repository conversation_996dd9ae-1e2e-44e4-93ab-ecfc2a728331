.key-numbers-item {
    margin: 0 0 30px;
    max-width: 100%;
    text-align: left;

    @include breakpoint(small down) {
        margin-bottom: 20px;
    }

    &__title {
        color: $color-3--4;
        font-size: 2.2rem;
        font-weight: var(--fw-normal);
        line-height: 1.2;
        margin-bottom: 20px;

        @include breakpoint(small down) {
            font-size: 2rem;
        }
    }

    &__chart-wrapper {
        align-items: center;
        display: flex;
    }

    &__value {
        color: $color-3--4;
        font-size: 1.6rem;
        margin-right: 10px;
        min-width: 37px;
    }

    &__chart {
        background-color: $color-3--2;
        flex-grow: 1;
        height: 21px;
    }

    &__chart-value {
        @include trs($duration: 500ms);
        @include size(0, 21px);
        background-color: var(--color-1--1);
    }
}
