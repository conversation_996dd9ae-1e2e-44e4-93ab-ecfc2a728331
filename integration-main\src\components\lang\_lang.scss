.lang-wrapper {
    @include absolute(0, null, null, 0);
    z-index: 101;
}

.lang {
    $this: &;

    @include size(100%);
    @include font(var(--typo-1), 1.2rem, var(--fw-normal));
    align-items: center;
    color: var(--color-1--1);
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    text-transform: uppercase;
    z-index: 10;

    @include breakpoint(medium down) {
        @include size(auto);
    }

    &.is-open {
        color: $color-white;

        #{$this}__toggle {
            background-color: $color-black;
        }

        #{$this}__toggle-icon {
            transform: rotateX(-180deg);
        }
    }

    &__toggle {
        @include size(54px, 45px);
        @include trs;
        align-items: center;
        background: transparent;
        border: 0;
        color: inherit;
        display: flex;
        flex-direction: row-reverse;
        justify-content: center;

        @include on-event {
            background-color: var(--color-1--1);
            color: $color-white;
        }
    }

    &__toggle-text {
        pointer-events: none;
    }

    &__toggle-icon {
        display: inline-block;
        font-size: 1.6rem;
        padding: 0 0 0 6px;
        pointer-events: none;
        position: relative;
        top: -1px;

        @include icon-after($fa-var-angle-down, null, center, var(--fw-normal));
    }

    &__block {
        margin-top: 1px;
        padding: 0;
        top: 67px;

        @include breakpoint(medium down) {
            top: 50px;
        }
    }

    &__link {
        @include trs;
        @include min-size(40px);
        align-items: center;
        background-color: $color-black;
        display: flex;
        justify-content: center;
        text-decoration: none;

        @include on-event {
            background-color: var(--color-1--1);
        }
    }

    .header:not(.js-fixed-el) & {
        body:not(.is-mnv-opened) .has-page-image:not(.has-secondary-heading) &,
        body:not(.is-mnv-opened).home-page &,
        body:not(.is-mnv-opened).home-hospital-page & {
            @include breakpoint(large) {
                color: $color-white;
            }
        }
    }
}
