.location {
    &__wrapper {
        align-items: flex-end;
        display: flex;
        justify-content: space-between;
        min-height: 370px;
        position: relative;

        @include breakpoint(medium down) {
            align-items: flex-start;
            min-height: auto;
        }

        @include breakpoint(small down) {
            align-items: center;
            flex-direction: column;
        }
    }
}

.location-directories {
    $this: &;

    max-width: 500px;

    @include breakpoint(medium down) {
        margin: 6px 35px 0 23px;
        max-width: 285px;
        width: 100%;
    }

    @include breakpoint(small down) {
        margin: 0 auto 35px;
        max-width: none;
        text-align: center;
    }

    &__title {
        @include font(var(--typo-1), 4.5rem, var(--fw-bold));
        color: $color-black;
        line-height: 5rem;
        margin: 0 0 10px;

        @include breakpoint(medium down) {
            font-size: 3.5rem;
            line-height: 4rem;
        }

        @include breakpoint(small down) {
            font-size: 2.8rem;
            line-height: 3.2rem;
        }
    }

    &__teaser {
        @include font(var(--typo-1), 1.7rem, var(--fw-normal));
        color: $color-black;
        line-height: 2.6rem;
    }

    &__list-items[class] {
        display: flex;
        justify-content: flex-start;
        margin: 40px -5px 0;

        @include breakpoint(medium down) {
            align-items: flex-start;
            flex-direction: column;
            margin: 35px 0 0 0;
        }

        @include breakpoint(small down) {
            margin: 35px auto 0;
            max-width: 245px;
        }
    }

    &__item {
        padding: 0 5px;

        @include breakpoint(medium down) {
            padding: 5px 0;
        }
    }

    &__link {
        align-items: center;
        color: var(--color-1--1);
        display: flex;
        flex-direction: column;
        text-decoration: none;

        @include breakpoint(medium down) {
            flex-direction: row;
            justify-content: flex-start;
        }

        @include on-event {
            #{$this}__link-title {
                background-color: var(--color-1--1);
                border-color: var(--color-1--1);
                color: $color-white;
            }
        }
    }

    &__link-icon {
        @include size(115px);
        display: block;
        flex-shrink: 0;
        margin: 0 0 15px;

        @include breakpoint(medium down) {
            @include size(60px, 50px);
            margin: 0 24px 0 0;
        }

        .is-three-items & {
            @include breakpoint(large only) {
                @include size(84px);
                margin: 0 0 20px;
            }
        }

        svg {
            @include size(100%);
            display: block;
            fill: var(--color-1--1);
        }
    }

    &__link-title {
        @include trs;
        @include font(var(--typo-1), 1.3rem, var(--fw-normal), normal);
        @include min-size(150px, 63px);
        align-items: center;
        background-color: transparent;
        border: 1px solid var(--color-1--1);
        color: var(--color-1--1);
        cursor: pointer;
        display: inline-flex;
        justify-content: center;
        letter-spacing: 0.1em;
        line-height: 2rem;
        margin: 0;
        padding: 22px 40px;
        text-decoration: none;
        text-transform: uppercase;
        width: auto;

        @include breakpoint(medium down) {
            font-size: 1.2rem;
            line-height: 1.8rem;
            min-height: 45px;
            padding: 12px 25px;
        }

        .is-three-items & {
            @include breakpoint(large only) {
                @include min-size(140px, 45px);
                padding: 12px 25px;
            }
        }
    }
}

.location-map {
    $this: &;

    @include absolute(46px, 510px, null, null);
    transform: translateX(50%);
    z-index: 1;

    @include breakpoint(medium down) {
        right: calc(50% - 400px);
        top: -80px;
        transform: translateX(-50%);
    }

    @include breakpoint(small down) {
        @include reset-position;
        display: flex;
        justify-content: center;
        margin-bottom: -85px;
        order: 1;
        transform: none;
    }

    &__figure {
        @include size(332px, 254px);

        @include breakpoint(medium down) {
            @include size(218px, 166px);
        }

        svg {
            @include size(100%);
            display: block;

            .stroke-outline {
                clip-path: ellipse(50% 43% at 50% 50%);
            }

            .color-1--1 {
                fill: var(--color-1--1); // #4269e2
            }

            .color-1--3 {
                fill: var(--color-1--3); // #c7d2f6
            }

            .color-1--4 {
                fill: var(--color-1--4); // #2e499e
            }

            .color-2--1 {
                fill: var(--color-2--1); // #ff8977
            }

            .color-2--2 {
                fill: var(--color-2--2); // #b26053
            }

            .color-2--3 {
                fill: var(--color-2--3); // #ffdcd6
            }
        }
    }
}

.location-description {
    $this: &;

    align-items: flex-start;
    background-color: $color-3--2;
    display: flex;
    flex-direction: column;
    justify-content: center;
    max-width: 510px;
    min-height: 370px;
    padding: 70px 0 70px 185px;
    position: relative;

    @include breakpoint(medium down) {
        align-items: center;
        flex-direction: column;
        max-width: none;
        padding: 100px 0 40px 30px;
        text-align: center;
        width: 100%;
    }

    @include breakpoint(small down) {
        order: 2;
        padding: 110px 0 40px;
    }

    &::after {
        @include absolute(0, null, null, 100%);
        @include size(100vw, 100%);
        background-color: $color-3--2;
        content: '';
        z-index: -1;

        @include breakpoint(small down) {
            left: 50%;
            transform: translateX(-50%);
        }
    }

    &__title {
        @include font(var(--typo-1), 4.5rem, var(--fw-bold));
        color: $color-black;
        line-height: 5rem;
        margin: 0 0 15px;

        @include breakpoint(medium down) {
            font-size: 3.5rem;
            line-height: 4rem;
        }

        @include breakpoint(small down) {
            font-size: 2.8rem;
            line-height: 3.2rem;
            margin: 0 auto 15px;
        }
    }

    &__teaser {
        @include font(var(--typo-1), 1.7rem, var(--fw-normal));
        color: $color-black;
        line-height: 2.6rem;

        + #{$this}__link {
            margin-top: 35px;
        }
    }

    & &__link {
        padding: 1.1em 2em 0.9em !important;

        @include breakpoint(small down) {
            margin: 28px auto 0;
        }
    }
}

.tooltip-map {
    @include absolute(0, null, null, 0);
    @include font(var(--typo-1), 1.2rem, var(--fw-normal));
    background-color: $color-3--4;
    color: $color-white;
    display: none;
    padding: 0.6em 1.2em;
    pointer-events: none;
    text-align: center;
    text-transform: uppercase;
    transform: translate(-50%, -50%);
    white-space: nowrap;
    z-index: 20;

    &::before {
        @include absolute(100%, null, null, 50%);
        border-left: 7px solid transparent;
        border-right: 7px solid transparent;
        border-top: 7px solid $color-3--4;
        content: '';
        transform: translateX(-50%);
    }
}
