.menu-floating {
    $this: &;

    @include fixed(50%, 0, auto, auto);
    transform: translateX(100%) translateX(-60px) translateY(-50%);
    transition: all 400ms ease 0ms;
    z-index: 100;

    @include breakpoint(medium down) {
        display: none;
    }

    &.is-hidden {
        opacity: 0;
        visibility: hidden;
    }

    &__toggle {
        display: none;
    }

    &__list {
        margin: 0;
        padding-left: 0;
    }

    &__item {
        margin-bottom: 1px;
        transition: transform 0.4s ease;
        will-change: transform;
    }

    &__link {
        @include trs;
        @include font(var(--typo-1), 1.4rem, var(--fw-normal));
        align-items: center;
        background-color: var(--color-2--1);
        color: $color-white;
        display: inline-flex;
        overflow: hidden;
        padding-right: 10px;
        position: relative;
        text-decoration: none;
        transform: translateX(0);
        z-index: 7;

        @include on-event {
            background-color: var(--color-2--1);
            transform: translateX(-100%) translateX(60px);
        }

        &::after {
            @include size(10px, 100%);
            @include absolute(0, null, null, 40px);
            @include trs;

            box-shadow: 7.5px 0 10px 0 rgba($color-black, 0.7);
            content: '';
            display: block;
            transform: rotate(5deg);
        }
    }

    &__icon {
        @include size(60px);

        align-items: center;
        background-color: inherit;
        display: flex;
        justify-content: center;
        margin-right: 10px;
        position: relative;
        z-index: 5;

        svg {
            @include size(25px);
            fill: $color-white;
            line-height: 60px;
        }
    }

    .main-nav & {
        @include relative();
        display: block;
        height: 100%;
        transform: none;
        z-index: auto;

        #{$this}__toggle {
            @include font(var(--typo-1), 1.6rem, var(--fw-bold));
            @include relative(0, 0);
            @include size(100%, 50px);
            background-color: var(--color-2--1);
            border: 0;
            color: $color-white;
            display: block;
            text-transform: uppercase;
        }

        #{$this}__toggle-icon {
            @include icon-before($fa-var-hand-point-up);
            margin-right: 5px;
        }

        #{$this}__list {
            display: none;
        }

        #{$this}__item {
            border-top: 1px solid $color-white;
            margin: 0;
        }

        #{$this}__link {
            background-color: var(--color-1--1);
            width: 100%;

            @include on-event {
                background-color: var(--color-2--1);
                transform: none;
            }
        }
    }
}
