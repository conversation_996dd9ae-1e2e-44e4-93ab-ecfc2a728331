.newsletter-bar {
    $this: &;

    &__container {
        @extend %container;
        @extend %container-horizontal-home;
    }

    &__wrap {
        align-items: center;
        background-color: $color-3--1;
        display: flex;
        flex-wrap: wrap;
        padding: 40px 50px;
        position: relative;

        @include breakpoint(medium down) {
            align-items: flex-start;
            padding: 40px 37px 26px 47px;
        }

        @include breakpoint(small down) {
            padding: 30px 30px 40px 35px;
        }
    }

    &__title {
        color: $color-black;
        line-height: 1;
        margin: 0;
        max-width: 280px;
        padding: 0;
        position: relative;
        text-transform: uppercase;

        @include breakpoint(medium down) {
            max-width: 220px;
        }

        @include breakpoint(small down) {
            margin: 0 auto;
            max-width: 230px;
        }

        .title {
            @include font(var(--typo-1), 3rem, var(--fw-normal));
            margin-bottom: 0;
            padding: 0;

            &__content {
                @include breakpoint(small down) {
                    text-align: left;
                }
            }

            &__svg {
                @include size(2em);
                display: block;
                margin-right: 22px;

                @include breakpoint(medium down) {
                    @include size(1.5em);
                    margin-right: 10px;
                }

                @include breakpoint(small down) {
                    margin-right: 15px;
                }

                svg {
                    @include size(60px);
                    display: block;

                    @include breakpoint(medium down) {
                        @include size(45px);
                    }
                }
            }

            &__text {
                font-size: 3rem;
                line-height: 1;

                @include breakpoint(medium down) {
                    font-size: 2.4rem;
                }

                @include breakpoint(small down) {
                    font-size: 2.6rem;
                }
            }

            &__subtext {
                @include font(var(--typo-1), 1.4rem, var(--fw-normal));
                display: block;
                line-height: 1.2;
                margin-top: 1px;
                text-transform: none;
            }
        }
    }

    &__form-wrapper {
        flex-grow: 1;
        margin: 0 0 0 65px;
        width: 1%;

        @include breakpoint(medium down) {
            margin: 0 0 0 30px;
        }

        @include breakpoint(small down) {
            margin: 24px 0 0;
            padding-left: 0;
            width: 100%;
        }
    }

    &__form[class] {
        align-items: center;
        display: flex;
        flex-wrap: wrap;

        @include breakpoint(small down) {
            align-items: flex-end;
        }

        .form {
            &__field-wrapper {
                margin-bottom: 0;

                .form__label {
                    color: $color-3--4;
                    margin: 5px 20px 5px 0;
                }
            }
        }
    }

    &__fields {
        flex-grow: 1;
        width: 1%;
    }

    &__actions {
        margin-left: 5px;
    }

    &__submit {
        @extend %button;
        //@extend %button-style-bordered;
        //@extend %button-style-bolded;

        @include breakpoint(medium down) {
            @include size(52px);
            min-width: auto;
            padding: 0;

            .btn__text {
                display: none;
            }
        }

        @include fa-icon-style(false) {
            @include breakpoint(large) {
                margin-right: 4px;
            }
        }
    }

    &__links {
        margin-left: 22px;
        width: 100px;

        @include breakpoint(medium down) {
            margin-left: 112px;
            margin-top: 8px;
            width: calc(100% - 112px);
        }

        @include breakpoint(small down) {
            margin-left: 0;
            margin-top: 10px;
            width: 100%;
        }
    }

    .newsletter-links {
        @include breakpoint(large only) {
            display: block;
        }

        &__item {
            line-height: 1;
        }

        &__item:not(:last-child) {
            line-height: 1;

            @include breakpoint(medium down) {
                margin-right: 20px;
            }

            @include breakpoint(small down) {
                margin-right: 15px;
            }
        }

        &__item-link {
            color: $color-3--4;
            line-height: 1;

            &.is-archives {
                font-weight: var(--fw-bold);
            }
        }
    }

    &.is-box {
        flex-shrink: 0;
        margin: 97px 0 0;
        width: 485px;

        @include breakpoint(medium down) {
            margin: 0 -20px;
            width: auto;
        }

        @include breakpoint(large) {
            #{$this}__wrap {
                display: block;
                padding: 60px 78px 80px;
            }

            #{$this}__form-wrapper {
                margin: 25px 0 0;
                width: 100%;
            }

            #{$this}__fields {
                margin: 0 0 25px;
                width: 100%;
            }

            #{$this}__actions {
                margin: 0;
            }

            .form__field-wrapper {
                display: block;
            }
        }
    }
}
