.newsletter {
    .form {
        width: 100%;

        &__fieldset {
            @include breakpoint(medium down) {
                align-items: flex-end;
                display: flex;
                justify-content: flex-start;
            }

            @include breakpoint(small down) {
                display: block;
                text-align: left;
            }
        }

        &__controls-group {
            display: flex;
            flex-wrap: wrap;
            margin-bottom: 10px;
            width: 100%;

            @include breakpoint(medium down) {
                display: inline-flex;
                flex-grow: 2;
                margin: 0 5px 0 30px;
                max-width: 390px;
                text-align: left;
            }

            @include breakpoint(small down) {
                margin: 0;
                max-width: none;
            }
        }

        &__legend {
            @include font(var(--typo-1), 2.4rem, var(--fw-bold), normal);
            align-items: center;
            color: $color-white;
            display: flex;
            line-height: 1;
            margin-bottom: 15px;

            @include breakpoint(medium down) {
                display: inline-flex;
                flex-grow: 1;
                justify-content: flex-end;
                margin-bottom: 8px;
                vertical-align: bottom;
                width: 1%;
            }

            @include breakpoint(small down) {
                justify-content: flex-start;
                width: 100%;
            }
        }

        &__legend-icon {
            @include size(26px);
            display: block;
            margin-right: 15px;

            svg {
                @include size(100%);
                display: block;
                fill: var(--color-2--1);
            }
        }

        &__legend-text {
            @include font(var(--typo-1), 2.4rem);
            color: inherit;
            text-transform: none;
        }

        &__label {
            color: $color-white;
            font-family: var(--typo-1);
            margin: 5px 0;
            width: 100%;

            @include breakpoint(small down) {
                margin: 10px 0 5px;
            }
        }

        &__field-wrapper {
            flex-grow: 1;
            margin-right: 5px;
            width: 1%;
        }

        input {
            @include font(null, 1.4rem);
            background-color: transparent;
            color: $color-white;
            font-weight: var(--fw-normal);
            min-height: 40px;
            padding: 8px 13px;
            position: relative;
            z-index: 2;

            &::placeholder {
                @include font(var(--typo-1), 1.4rem, var(--fw-normal));
                color: $color-white;
            }
        }
    }
}
