.numbers-item {
    $this: &;
    @extend %link-block-context;
    display: flex;

    &__icon {
        @include size(90px);
        flex-shrink: 0;
        margin-right: 20px;

        @include breakpoint(small down) {
            @include size(60px);
            margin-right: 16px;
        }

        svg {
            @include size(100%);
            fill: var(--color-1--1);
        }
    }

    &__content {
        @include line-decor(35px, 4px, 'after');
        padding-bottom: 20px;

        &::after {
            transform: translateY(calc(24px - 100%));
        }
    }

    a#{$this}__text {
        @extend %link-block;
        display: block;
        text-decoration: underline;

        @include on-event {
            outline: none !important;
            text-decoration: none;
        }
    }

    &__title {
        @include font(var(--typo-1), 3.5rem, var(--fw-bold));
        display: block;

        @include breakpoint(small down) {
            font-size: 2.8rem;
        }
    }

    &__description {
        @include font(var(--typo-1), 1.9rem, var(--fw-normal));
        display: block;

        @include breakpoint(small down) {
            font-size: 1.6rem;
            line-height: 1.4;
        }
    }
}
