.partners {
    background-color: $color-white;
    padding: 7px 0;

    @include breakpoint(medium down) {
        padding: 40px 0;
    }

    &__container {
        @extend %container;
        display: flex;
        justify-content: center;

        @include breakpoint(medium down) {
            display: block;
            padding: 0 108px;
        }

        @include breakpoint(small down) {
            padding: 0 14px;
        }
    }

    &__title {
        @include font(null, 2.2rem, var(--fw-normal));
        color: $color-3--4;
        flex-shrink: 0;
        margin: 40px 70px 0 0;
        text-align: center;

        @include breakpoint(medium down) {
            margin: 0 0 6px;
        }
    }

    &__list {
        align-items: center;
        display: flex;
        flex-wrap: wrap;
        min-height: 120px;

        @include breakpoint(medium down) {
            justify-content: center;
            min-height: auto;
        }
    }

    &__item {
        padding: 0 17px;

        @include breakpoint(medium down) {
            padding: 0 4px;
            width: calc(100% / 5);
        }

        @include breakpoint(small down) {
            width: calc(100% / 3);
        }
    }

    &__link {
        align-items: center;
        background-color: $color-white;
        display: flex;
        justify-content: center;
        padding: 10px 8px;
    }

    &__image {
        pointer-events: none;

        img {
            @include max-size(150px, 100px);
            width: auto;

            @include breakpoint(medium down) {
                max-width: 100%;
            }
        }
    }
}
