.publications-content-item {
    $this: &;

    @extend %link-block-context;
    align-items: flex-start;
    display: flex;

    @include breakpoint(small down, true) {
        align-items: initial;
        flex-direction: column;
        text-align: center;
    }

    &__image {
        @include size(223px, auto);
        display: inline-block;
        flex-shrink: 0;
        position: relative;
        transform: rotate(-5deg);

        @include breakpoint(medium down, true) {
            width: 175px;
        }

        @include breakpoint(small down, true) {
            margin: 0 auto;
            width: 161px;
        }

        img {
            box-shadow: 0 0 20px rgba($color-black, 0.16);
        }
    }

    &__content {
        flex-grow: 1;
        padding: 30px 0 0 45px;

        @include breakpoint(medium down, true) {
            padding: 0 0 0 35px;
        }

        @include breakpoint(small down, true) {
            padding: 30px 0 0;
        }
    }

    &__category {
        margin-bottom: 13px;

        @include breakpoint(small down, true) {
            font-size: 1.4rem;
        }
    }

    &__title {
        font-size: 3.5rem;
        margin-bottom: 32px;
        max-width: 100%; //For IE

        @include breakpoint(medium down, true) {
            margin-bottom: 23px;
        }

        @include breakpoint(small down, true) {
            font-size: 2.4rem;
        }
    }

    &__title-link {
        @extend %link-block;
        @extend %underline-context;
    }

    &__subtitle {
        @include font(null, 3rem, var(--fw-normal));
        display: block;
        margin: 5px 0 0;

        @include breakpoint(medium down, true) {
            font-size: 2.5rem;
        }

        @include breakpoint(small down, true) {
            font-size: 2.2rem;
        }
    }

    &__publication[class] {
        margin-top: 20px;

        @include breakpoint(small down, true) {
            padding-left: 0;

            &::before {
                content: none;
            }
        }
    }

    &__bottom-wrapper {
        display: flex;

        @include breakpoint(medium down, true) {
            display: block;
        }

        @include breakpoint(small down, true) {
            display: block;
        }
    }

    &__teaser {
        margin: 0 30px 20px 0;

        @include breakpoint(medium down, true) {
            margin-right: 0;
        }

        @include breakpoint(small down, true) {
            margin-right: 0;
        }
    }

    &__actions {
        align-items: flex-end;
        display: flex;
        flex-shrink: 0;
        position: relative;
        z-index: 3;

        @include breakpoint(small down, true) {
            display: block;
        }

        .document-actions {
            @include breakpoint(small down, true) {
                align-items: center;
            }
        }
    }
}
