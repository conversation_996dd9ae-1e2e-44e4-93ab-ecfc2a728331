.quicklinks-info {
    @extend %link-block-context;
    align-items: flex-start;
    background-color: var(--color-1--1);
    color: $color-white;
    display: flex;
    flex-direction: column;
    flex-shrink: 0;
    padding: 50px 60px;
    position: relative;
    width: 384px;

    @include breakpoint(medium down) {
        padding: 40px;
        width: 256px;
    }

    @include breakpoint(small down) {
        align-items: center;
        padding: 20px 70px;
        width: 100%;
    }

    &__text {
        display: flex;
    }

    &__svg-wrapper {
        @include size(60px);
        flex-shrink: 0;
        margin-right: 10px;

        @include breakpoint(medium down) {
            @include size(35px);
        }

        svg {
            @include size(100%);
            fill: $color-white;
        }
    }

    &__title {
        @include font(var(--typo-1), 2.8rem, var(--fw-bold));
        line-height: 1.25;

        @include breakpoint(medium down) {
            font-size: 1.8rem;
        }
    }

    &__link {
        @extend %link-block;
        @extend %underline-context;

        .underline {
            @include multiline-underline();
        }

        &:focus-visible {
            &::after {
                outline-offset: -3px;
            }
        }
    }

    &__teaser {
        @include font(var(--typo-1), 1.6rem, var(--fw-normal));
        line-height: calc(18 / 16);
        margin: 10px 0 0;

        @include breakpoint(medium down) {
            font-size: 1.3rem;
            line-height: calc(16 / 13);
            margin: 2px 0 0;
        }

        @include breakpoint(small down) {
            display: none;
        }
    }

    &__icon {
        @include absolute(null, 30px, 35px, null);
        @include font(null, 3rem, var(--fw-normal));
        color: $color-white;

        @include breakpoint(medium down) {
            bottom: 20px;
            font-size: 2rem;
            right: 30px;
        }

        @include breakpoint(small down) {
            bottom: 10px;
        }
    }
}
