.reviews-item {
    $this: &;

    @extend %link-block-context;
    align-items: flex-start;
    display: flex;
    flex-grow: 1;
    max-width: 100%;
    width: 100%;

    @include breakpoint(small down) {
        align-items: center;
        flex-direction: column;
    }

    &__picture-wrap {
        @include size(250px, auto);
        @include decor-mask('quote', var(--color-2--1), 86px, 86px, 'after');
        flex-shrink: 0;
        position: relative;

        @include breakpoint(medium down) {
            width: 200px;
        }

        &::after {
            @include absolute(25px, -63px, null, null);
            z-index: 1;

            @include breakpoint(medium down) {
                @include size(70px);
                right: -22px;
                top: 4px;
            }

            @include breakpoint(small down) {
                @include size(85px);
                bottom: -40px;
                left: 50%;
                top: auto;
                transform: translateX(-50%);
            }
        }
    }

    &__picture {
        @include size(100%, auto);

        img {
            @include object-fit();
            display: block;
        }
    }

    &__content {
        flex-grow: 1;
        padding-left: 80px;
        width: 1%;

        @include breakpoint(medium down) {
            padding-left: 34px;
        }

        @include breakpoint(small down) {
            padding: 38px 0 0 0;
            text-align: center;
            width: 100%;
        }
    }

    &__category {
        @include font(null, 1.4rem, var(--fw-normal));
        color: var(--color-1--1);
        letter-spacing: 2.52px;
        margin-bottom: 13px;
        text-transform: uppercase;

        @include breakpoint(small down) {
            font-size: 1.2rem;
        }
    }

    &__quote {
        @include font(var(--typo-1), 2rem, var(--fw-normal));
        color: $color-3--4;
        line-height: 1.55;
        margin: 0 0 20px;
        padding-bottom: 25px;
        position: relative;

        @include breakpoint(medium down) {
            font-size: 1.8rem;
        }

        &::after {
            @include absolute(null, null, 0, 0);
            @include size(35px, 4px);
            background-color: var(--color-2--1);
            content: '';
            display: block;

            @include breakpoint(small down) {
                left: 50%;
                transform: translateX(-50%);
            }
        }
    }

    &__name {
        @include font(var(--typo-1), 2.2rem, var(--fw-bold));
        line-height: 1.25;
        margin: 0 0 5px;

        @include breakpoint(medium down) {
            font-size: 2rem;
        }
    }

    &__name-link {
        @extend %link-block;
        @extend %underline-context;
    }

    &__function {
        @include font(var(--typo-1), 1.4rem, var(--fw-normal));
        color: $color-3--4;
        line-height: 1.25;
    }

    &__teaser {
        margin-top: 10px;
    }

    .reviews-home & {
        &__picture-wrap {
            width: 384px;

            @include breakpoint(medium down) {
                width: 242px;
            }

            &::after {
                @include size(147px);
                right: -110px;
                top: -6px;

                @include breakpoint(medium down) {
                    @include size(85px);
                    right: -25px;
                    top: -9px;
                }

                @include breakpoint(small down) {
                    bottom: -32px;
                    right: 50%;
                    top: auto;
                    transform: translateX(-50%);
                }
            }
        }

        &__content {
            padding: 22px 0 0 126px;

            @include breakpoint(medium down) {
                padding: 0 0 0 35px;
            }

            @include breakpoint(small down) {
                padding: 42px 0 0;
            }
        }

        &__quote {
            font-size: 2.8rem;

            @include breakpoint(medium down) {
                font-size: 2rem;
            }

            @include breakpoint(small down) {
                font-size: 1.8rem;
            }
        }

        &__name {
            font-size: 2.4rem;

            @include breakpoint(medium down) {
                font-size: 2rem;
            }
        }
    }
}
