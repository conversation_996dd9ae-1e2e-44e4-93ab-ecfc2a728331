.search-services {
    $this: &;

    margin: -93px 0 90px;
    position: relative;
    z-index: 2;

    @include breakpoint(medium down) {
        margin-top: -60px;
    }

    @include breakpoint(small down) {
        margin-top: 0;
    }

    &.is-opened {
        #{$this}__title {
            color: $color-black;

            svg {
                fill: var(--color-1--1);
            }
        }

        #{$this}__wrap {
            background-color: $color-white;
        }

        #{$this}__list-item {
            border-color: $color-3--4;
        }

        #{$this}__list-toggle {
            color: $color-black;

            &::before {
                background-color: $color-3--4;
            }

            &.is-open,
            &:hover,
            &:focus {
                color: var(--color-1--1);

                #{$this}__list-toggle-text {
                    &::after {
                        background-color: var(--color-1--1);
                        width: 100%;
                    }
                }
            }
        }
    }

    &__container {
        @extend %container;
        max-width: 1200px;
        padding: 0;
        position: relative;

        @include breakpoint(medium down) {
            padding: 0 62px;
        }

        @include breakpoint(small down) {
            padding: 0;
        }
    }

    &__wrap {
        @include trs($duration: 100ms);
        align-items: center;
        background-color: var(--color-1--1);
        box-shadow: 0 0 50px rgba(0, 0, 0, 0.16);
        display: flex;
        justify-content: space-between;
        padding: 60px 100px;
        position: relative;

        @include breakpoint(medium down) {
            flex-direction: column;
            padding: 50px 62px;
        }

        @include breakpoint(small down) {
            padding: 25px 20px 35px;
        }
    }

    &__title {
        @include trs;
        @include font(var(--typo-1), 3.5rem, var(--fw-bold));
        align-items: center;
        color: $color-white;
        display: inline-flex;
        line-height: 4rem;
        margin: 0 75px 0 0;

        @include breakpoint(medium down) {
            font-size: 2.8rem;
            margin: 0 0 13px;
        }

        @include breakpoint(small down) {
            display: block;
            font-size: 2.8rem;
        }

        svg {
            @include trs;
            @include size(60px, 66px);
            display: block;
            fill: $color-white;
            flex-shrink: 0;
            margin-right: 15px;

            @include breakpoint(medium down) {
                @include size(46px, 42px);
            }

            @include breakpoint(small down) {
                margin: 0 auto 5px;
            }
        }
    }

    & &__list {
        display: flex;
        flex-shrink: 0;
        margin: 0 -30px;

        @include breakpoint(small down) {
            align-items: center;
            flex-direction: column;
            width: 100%;
        }
    }

    &__list-item {
        align-items: center;
        border-left: 1px solid var(--color-1--3);
        display: flex;
        max-width: 232px;
        padding: 0 30px;
        position: static;

        @include breakpoint(small down) {
            border: 0;
            display: flex;
            flex-direction: column;
            margin-bottom: 10px;
            max-width: 520px;
            padding: 0;
        }

        &:first-child {
            border: 0;

            #{$this}__list-toggle:before {
                content: none;
            }
        }

        &.is-open {
            #{$this}__panel {
                display: block;
            }
        }
    }

    &__list-toggle {
        @include focus-outline($offset: 3px);
        @include font(var(--typo-1), 2rem, var(--fw-normal));
        background: transparent;
        border: 0;
        border-radius: 0;
        color: $color-white;
        cursor: pointer;
        min-height: 30px;
        padding: 0;
        position: relative;

        &::before {
            @include breakpoint(small down) {
                @include trs;
                @include absolute(-4px, null, null, 50%);
                @include size(30px, 1px);
                background-color: var(--color-1--3);
                content: '';
                transform: translateX(-50%);
            }
        }

        @include on-event {
            #{$this}__list-toggle-text {
                &::after {
                    width: 100%;
                }
            }
        }
    }

    &__list-toggle-text {
        @include trs;
        display: block;
        position: relative;

        &::after {
            @include absolute(null, null, -2px, 50%);
            @include trs;
            @include size(0, 4px);
            background-color: $color-white;
            content: '';
            transform: translateX(-50%);
            z-index: 11;
        }
    }

    &__panel {
        @include absolute(100%, 0, null, 0);
        background-color: $color-white;
        box-shadow: 0 25px 25px rgba(0, 0, 0, 0.16);
        display: none;
        padding: 0 50px 50px;
        z-index: 10;

        @include breakpoint(medium down) {
            padding: 0 50px 60px;
        }

        @include breakpoint(small down) {
            @include relative(auto, auto, auto, auto);
            box-shadow: none;
            padding: 40px 0 30px;
        }
    }

    & &__close {
        @include absolute(-5px, 40px);
        border: 0;
        z-index: 1;

        @include breakpoint(medium down) {
            right: 20px;
            top: -7px;
        }

        @include breakpoint(small down) {
            right: -13px;
            top: 32px;
        }

        @include fa-icon-style(false) {
            font-size: 3rem;
        }
    }

    .form {
        margin: 0;

        &__legend {
            @include font(var(--typo-1), 2.8rem, var(--fw-bold));
            line-height: 3.2rem;
            margin-bottom: 40px;
            padding-right: 40px;

            @include breakpoint(small down) {
                font-size: 2.4rem;
                line-height: 2.6rem;
            }
        }

        &__field-wrapper {
            margin: 0 0 25px;

            @include breakpoint(medium down) {
                align-items: flex-start;
                flex-direction: column;
                margin: 0 0 20px;
            }
        }

        &__actions {
            margin: 15px 0 0;

            @include breakpoint(medium down) {
                margin-top: 20px;
            }

            button {
                margin: 0;
            }
        }
    }
}
