.list.is-list-search {
    li {
        margin-bottom: 40px;

        @include breakpoint(medium down) {
            margin-bottom: 20px;
        }
    }
}

.search-item {
    $this: &;
    @extend %link-block-context;
    border-bottom: 1px solid $color-3--3;
    padding-bottom: 40px;

    @include breakpoint(medium down) {
        padding-bottom: 20px;
    }

    @include breakpoint(xsmall down) {
        display: flex;
        flex-direction: column;
    }

    &__category {
        margin: 0 0 10px;
        width: calc(100% - 200px);

        @include breakpoint(xsmall down) {
            width: 100%;
        }

        ~ #{$this}__title-link {
            padding: 0;
        }
    }

    &__title {
        margin: 0 0 10px;

        mark {
            position: relative;
            z-index: -1;
        }
    }

    &__title-link {
        @extend %link-block;
        @extend %underline-context;
        display: block;
        padding-top: 20px;

        @include breakpoint(xsmall down) {
            padding: 0;
        }
    }

    &__teaser {
        margin: 0 0 10px;
    }

    &__relevance {
        @include absolute(-5px, 0, null, null);
        align-items: center;
        display: flex;
        justify-content: flex-end;
        width: 200px;

        @include breakpoint(xsmall down) {
            @include reset-position;
            justify-content: flex-start;
            margin: 0 0 10px;
            order: -1;
        }
    }

    &__relevance-label {
        margin-right: 10px;
    }

    &__relevance-label,
    &__relevance-percent {
        @include font(var(--typo-1), 1.4rem, var(--fw-normal));
    }

    &__relevance-bar {
        @include size(65px, 8px);
        background-color: $color-3--3;
        display: block;
        margin-right: 10px;
        position: relative;
    }

    &__relevance-bar-bg {
        @include absolute(0, null, null, 0);
        background-color: var(--color-1--1);
        height: 100%;
    }

    &.is-relevant {
        border: 1px solid $color-3--3;
        display: flex;
        flex-direction: column;
        padding: 40px;

        @include breakpoint(small down) {
            padding: 20px;
        }

        #{$this}__date {
            margin: 0 0 5px;
            order: -1;
        }

        #{$this}__category {
            width: 100%;
        }

        #{$this}__title-link {
            padding: 0;
        }

        #{$this}__teaser {
            margin: 0;
        }
    }
}
