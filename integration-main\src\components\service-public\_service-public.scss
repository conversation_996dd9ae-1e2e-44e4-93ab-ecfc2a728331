// Generic
.entiteImageFloatLeft {
    float: left;
    margin: 5px 10px 10px 0;
}

.entiteImageFloatRight {
    float: right;
    margin: 5px 0 10px 10px;
}

// Breadcrumb
.breadcrumb.spFilDAriane {
    display: block;
    margin-bottom: 40px;
    vertical-align: inherit;
    width: auto;

    p {
        font-size: 1.4rem;

        a {
            text-decoration: none;

            @include on-event {
                text-decoration: underline;
            }
        }
    }
}

.spCenter {
    h1 {
        @extend %rte-heading;
        color: var(--color-2--1);
        font-size: #{4*1.2}rem;
        font-weight: var(--fw-bold);
        margin: em(6, 4) 0 em(2.5, 4);
    }
}

// Block title
.rte .spBlockTitle {
    color: $color-black;
    font-size: 1.4rem;
    font-weight: var(--fw-bold);
}

// Paragraphs
.spIntroduction p,
.spDescription {
    $teaser-font-size: 1.8;

    color: $color-3--4;
    font-size: #{$teaser-font-size}rem;
    margin: 11px 0;
}

$attention-icon: \f071;
$noter-icon: \f044;
$savoir-icon: \f0c6;

// Specific paragraph blocs
.spAttention,
.spANoter,
.spASavoir {
    background: $color-3--1;
    margin: 20px 0;
    overflow: hidden;
    padding: 15px 15px 15px 45px;
    position: relative;

    *[aria-hidden='true'] {
        display: none;
    }

    @include icon-before($savoir-icon);

    &::before {
        @include absolute(15px, null, null, 15px);
        font-size: 1.6rem;
    }

    p:first-of-type {
        margin-top: 0;
    }

    p:last-of-type {
        margin-bottom: 0;
    }
}

.spAttention {
    background: $color-red;

    &::before {
        content: unquote('\"#{ $attention-icon }\"');
    }
}

.spANoter {
    background: var(--color-2--1); // lighten($color-2--2, 55%)

    &::before {
        content: unquote('\"#{ $noter-icon }\"');
    }
}

.spASavoir {
    background: $color-green;
}

.spLienExterne,
.spLienInterne {
    color: var(--color-1--2);
    text-decoration: underline;

    @include on-event {
        text-decoration: none;
    }
}

// Home menu
.rte ul.co-home-menu {
    display: flex;
    flex-wrap: wrap;
    font-size: 1rem;
    margin: 20px 0 !important;
    overflow: visible !important;
    padding-left: 0;
    text-align: center;

    > * {
        flex: 1 1 33.33334%;
    }

    > li {
        background-image: none;
        border: 1px solid $color-3--2;
        font-size: 1rem;
        margin: 0 -1px -1px 0 !important;
        padding: 15px;

        &::before {
            content: none !important;
            display: none;
        }

        h3 {
            color: var(--color-1--1);
            font-size: 1.8rem;
            font-weight: var(--fw-normal);
            margin: 0 0 10px;
            text-transform: uppercase;

            a {
                color: var(--color-1--1);
                font-weight: 700;
                text-decoration: none;

                @include on-event {
                    color: $color-white;
                }
            }
        }
    }

    .co-home-sousmenu {
        margin: 0 !important;

        > li {
            background-image: none;
            display: inline;
            padding: 0 !important;

            &::before {
                content: '' !important;
                display: none !important;
            }

            a {
                color: $color-black;
                text-decoration: underline;

                @include on-event {
                    color: $color-white;
                    text-decoration: none;
                }
            }
        }
    }
}

.comarquage {
    .rte {
        .click-and-roll:only-child {
            .click-and-roll__toggle-wrapper {
                margin-top: 0 !important;
            }
        }

        &[data-sd-content='more-info-text'] {
            display: none;
        }
    }

    .click-roll-content {
        &.flex-column {
            display: flex;
            flex-direction: column;

            .js-click-and-roll-toggle-all {
                align-self: flex-end;
            }
        }
    }

    .form {
        .legend {
            &__btn {
                margin-left: 15px;

                @include fa-icon-style(false) {
                    @include trs();
                    color: var(--color-1--2);
                    cursor: pointer;
                    font-size: 2.5rem;

                    @include on-event() {
                        color: var(--color-1--1);
                    }
                }
            }
        }
    }
}

.co-home-img {
    display: none;
}
