.menu-services {
    $this: &;
    margin-top: -70px;

    @include breakpoint(medium down) {
        margin-top: -40px;
    }

    &__wrapper {
        @extend %container;
        @extend %container-lg;
    }

    ul#{$this}__list {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        margin: -15px -15px 0;
        padding-bottom: 20px;

        @include breakpoint(medium down) {
            margin: -10px -10px 0;
        }

        @include breakpoint(small down) {
            margin: -5px -5px 0;
        }
    }

    &__item {
        padding: 15px;

        @include breakpoint(medium down) {
            padding: 10px;
        }

        @include breakpoint(small down) {
            padding: 5px;
        }
    }

    &__link {
        @include font(null, 1.7rem, var(--fw-normal));
        @extend %link-block-context;
        @extend %underline-context;
        background-color: $color-white;
        color: $color-3--3;
        display: block;
        line-height: 1;
        padding: 20px 30px;
        text-decoration: none;

        @include breakpoint(small down) {
            font-size: 1.6rem;
            padding: 10px 20px;
        }
    }
}
