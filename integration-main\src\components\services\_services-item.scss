.services-item {
    @extend %link-block-context;
    background-color: $color-3--1;
    display: flex;
    justify-content: space-between;
    position: relative;

    @include breakpoint(medium down) {
        flex-wrap: wrap;
    }

    &__wrapper {
        align-items: flex-start;
        display: flex;
        padding: 35px 25px 35px 35px;

        @include breakpoint(medium down) {
            padding: 35px;
        }

        @include breakpoint(small down) {
            flex-direction: column;
            padding: 30px 30px 25px;
            text-align: center;
        }
    }

    &__picture {
        display: block;
        flex-grow: 0;
        flex-shrink: 0;
        margin: 0 35px 0 0;

        @include breakpoint(medium down) {
            margin: 0 25px 0 0;
            max-width: 200px;
        }

        @include breakpoint(small down) {
            margin: 0 auto 20px;
        }
    }

    &__title {
        @include font(var(--typo-1), 2.4rem, var(--fw-bold));
        color: $color-black;
        line-height: 2.8rem;
        margin: 0;

        @include breakpoint(medium down) {
            font-size: 2.2rem;
            line-height: 2.4rem;
        }

        @include breakpoint(small down) {
            font-size: 2rem;
        }
    }

    &__title-link {
        @extend %link-block;
        @extend %underline-context;
    }

    &__name {
        @include font(var(--typo-1), 1.8rem, var(--fw-bold));
        color: var(--color-1--1);
        line-height: 2.2rem;
        margin: 20px 0 0;
    }

    &__name-position {
        @include font(var(--typo-1), 1.6rem, var(--fw-normal));
        color: $color-black;
        display: block;
        margin-top: 5px;
    }

    &__info {
        background-color: $color-3--2;
        flex-shrink: 0;
        padding: 50px 35px 50px 50px;
        width: 437px;

        @include breakpoint(medium down) {
            padding: 30px 35px 40px;
            width: 100%;
        }

        @include breakpoint(small down) {
            padding: 40px 30px 30px;
        }
    }

    &__info-title {
        @include font(var(--typo-1), 1.8rem, var(--fw-normal));
        color: $color-black;
        line-height: 2.2rem;
        margin: 0 0 5px;
    }

    &__info-teaser {
        @include font(var(--typo-1), 1.6rem, var(--fw-normal));
        color: $color-black;
        line-height: 2.2rem;
        margin: 0;
    }
}
