.sitemap-item {
    $this: &;

    &__title {
        @include font(var(--typo-1), 2.8rem, var(--fw-bold));
        color: var(--color-1--4);
        line-height: 1;
        margin: 0 0 20px;

        a {
            display: inline-block;
            padding-bottom: 15px;
            position: relative;
            text-decoration: none;

            &::after {
                @include trs;
                @include absolute(null, null, 0, 0);
                @include size(35px, 4px);
                background-color: var(--color-2--1);
                content: '';
            }

            @include on-event {
                &::after {
                    width: 100%;
                }
            }
        }
    }

    &__teaser {
        @include font(var(--typo-1), 1.7rem, var(--fw-normal));
        color: $color-3--4;
        line-height: 2.6rem;
        margin: 0 0 25px;
    }

    &__list[class] {
        position: relative;

        &::before {
            @include absolute(12px, null, null, 0);
            @include size(2px, calc(100% - 20px));
            background-color: var(--color-1--1);
            content: '';
        }

        &.-lvl-1 {
            margin: 0 65px;

            @include breakpoint(small down) {
                margin: 0;
            }
        }

        &.-lvl-2 {
            margin: 13px 0 0 35px;

            &::before {
                background-color: var(--color-1--3);
            }

            #{$this}__link {
                color: $color-black;
                font-size: 1.7rem;
                font-weight: var(--fw-normal);
                padding-left: 15px;

                &::before {
                    background-color: var(--color-1--3);
                    border-radius: 50%;
                }

                .underline {
                    @include multiline-underline();
                }
            }
        }
    }

    &__listitem {
        @extend %link-block-context;
        margin-bottom: 13px;
    }

    &__link {
        @extend %link-block;
        @extend %underline-context;
        @include font(var(--typo-1), 2.2rem, var(--fw-bold));
        color: var(--color-1--1);
        display: inline-block;
        line-height: 2.4rem;
        padding-left: 20px;
        position: relative;
        text-decoration: none;

        &::before {
            @include absolute(8px, null, null, -4px);
            @include size(10px);
            background-color: var(--color-1--1);
            content: '';
        }

        .underline {
            @include multiline-underline();
        }
    }
}
