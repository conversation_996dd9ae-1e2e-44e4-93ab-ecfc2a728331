// @name Steps
.steps {
    color: $color-black;
    font-family: var(--typo-1);
    margin: 10px 0 20px;

    &__title {
        font-size: 1.4rem;
        font-weight: var(--fw-bold);
        line-height: 1;
        margin: 5px 0;
        text-transform: uppercase;
    }

    &__description {
        display: block;
        font-size: 1.2rem;
        font-weight: var(--fw-normal);
        margin: 5px 0 10px;
    }

    &__bar {
        display: flex;
        justify-content: space-between;
    }

    &__item {
        @include size(100%, 10px);
        background-color: $color-3--3;
        border-right: 1px solid $color-white;

        &:first-child {
            border-radius: 2px 0 0 2px;
        }

        &:last-child {
            border: 0;
            border-radius: 0 2px 2px 0;
        }

        &.is-complete {
            background-color: var(--color-1--1);
        }
    }
}
