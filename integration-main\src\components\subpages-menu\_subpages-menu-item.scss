.subpages-menu {
    $this: &;

    .list__item {
        margin-bottom: 50px;
        max-width: 480px;

        @include breakpoint(medium down) {
            margin-bottom: 45px;
        }
    }

    &__item {
        @extend %link-block-context;
        display: flex;
        flex-direction: column-reverse;
        margin: 0 auto;
        max-width: 480px;
    }

    &__content {
        @include line-decor(35px, 4px, 'before');
        background-color: $color-white;
        margin: -42px 24px 0 0;
        max-width: 80%;
        padding: 20px 24px 0 0;

        &::before {
            transform: translate(22px, -22px);
        }
    }

    &__picture {
        display: block;
        position: relative;
        z-index: -1;
    }

    &__title {
        color: var(--color-1--1);
        margin: 0;

        a {
            //background-image: linear-gradient(transparent calc(100% - 2px), var(--color-1--1) 2px);
            //background-position-y: -4px;
            //color: inherit;
        }
    }

    &__title-link {
        @extend %link-block;
        @extend %underline-context;

        .underline {
            @include multiline-underline();
        }
    }

    &__teaser {
        margin: 25px 0 20px;
    }

    &__list[class] {
        border-top: 1px solid $color-3--2;
        margin: 0;
        padding: 5px 0 0;
        position: relative;
        z-index: 4;
    }

    &__listitem {
        @include font(var(--typo-1), 1.4rem, var(--fw-normal));
        color: $color-black;
        margin: 10px 0;
        padding-left: 14px;
        position: relative;
        text-transform: uppercase;

        @include fa-icon-style(false) {
            @include absolute(3px, null, null, 0);
            color: var(--color-2--1);
        }

        a {
            color: $color-black;
            display: block;
            text-decoration: none;
        }

        @include on-event {
            color: var(--color-1--2);

            @include fa-icon-style(false) {
                color: var(--color-1--2);
            }

            a {
                color: var(--color-1--2);
                text-decoration: underline;
            }
        }
    }
}
