.visit-link-widget {
    @include breakpoint(medium down) {
        display: flex;
        justify-content: center;
    }
}

.visit-link {
    @include icon-before($fa-var-angle-right);
    font-size: 1.8rem;
    line-height: 1.1;
    padding: 20px 85px 20px 20px;
    $this: &;

    @include breakpoint(xsmall down) {
        padding-right: 110px;
    }

    &::before {
        @include absolute(50%, 30px);
        font-size: 2rem;
        font-weight: var(--fw-normal);
        transform: translate(0, -50%);
    }

    &::after {
        @include absolute(20px, 57px, 20px);
        @include size(1px, auto);
        background-color: var(--color-1--2);
        content: '';
    }

    span[class*="fa-"] {
        font-size: 3.1rem;
        margin-right: 8px;
    }
}
