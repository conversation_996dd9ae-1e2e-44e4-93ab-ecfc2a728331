.vote {
    $this: &;
    background-color: $color-3--1;
    margin: 90px 0;

    @include breakpoint(medium down) {
        margin: 60px 0;
    }

    @include breakpoint(small down) {
        margin: 40px 0;
    }

    &:last-child {
        @include breakpoint(large) {
            margin-bottom: 0;
        }
    }

    &.is-message-visible {
        #{$this}__message {
            opacity: 1;
            position: static;
            visibility: visible;
        }
    }

    &__wrapper {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        margin: 0 auto;
        max-width: 800px;
        padding: 20px 35px;
        position: relative;

        @include breakpoint(small down) {
            padding: 30px 35px;
        }

        > * + * {
            margin-top: 40px;
        }
    }

    &__message {
        align-items: center;
        display: inline-flex;
        justify-content: center;
        width: 100%;

        &:not(:last-child) {
            margin-bottom: 40px;
        }

        @include fa-icon-style {
            color: $color-green;
            font-size: 3.8rem;
            font-weight: 400;
            margin-right: 7px;
        }
    }

    &__message-title {
        @include font(var(--typo-1), 1.8rem, normal);
    }

    .form {
        margin: 0;
        width: 100%;

        &__fieldset,
        &__fieldset:only-child {
            align-items: center;
            display: flex;
            justify-content: space-around;
            margin: 0;
            max-width: 1260px;
            padding: 0;

            @include breakpoint(small down) {
                flex-direction: column;
            }
        }

        &__legend {
            @include font(var(--typo-1), 1.8rem, var(--fw-normal));
            color: $color-3--4;
            margin: 0;
            width: auto;

            @include breakpoint(small down) {
                margin: 0 36px;
                text-align: center;
            }
        }

        &__actions {
            gap: 14px;
            margin: 0 0 0 15px;

            @include breakpoint(small down) {
                margin: 20px 0 0;
            }

            .btn {
                @include fa-icon-style(false) {
                    font-size: 1.4rem;
                }
            }
        }
    }
}
