.works-info-item {
    $this: &;

    @extend %link-block-context;
    background-color: $color-3--1;
    display: flex;
    flex-direction: row-reverse;
    justify-content: flex-end;
    padding: 35px;

    @include breakpoint(medium down) {
        flex-direction: column-reverse;
    }

    &__content-top {
        display: flex;
        flex-direction: column;
        flex-shrink: 0;

        @include breakpoint(medium down) {
            flex-direction: row;
            margin: 0 0 30px;
        }

        @include breakpoint(small down) {
            align-items: center;
            flex-direction: column;
        }
    }

    &__picture {
        margin-right: 60px;
        max-width: 282px;

        @include breakpoint(medium down) {
            margin: 0;
            max-width: 200px;
        }

        + #{$this}__date {
            margin: -35px 0 0;
            z-index: 2;

            @include breakpoint(medium down) {
                margin: auto 0 auto -78px;
            }

            @include breakpoint(small down) {
                margin: 0 auto;
            }
        }
    }

    &__date {
        display: flex;
        flex-direction: column;
        margin: 40px 45px 0 0;

        @include breakpoint(medium only) {
            align-items: stretch;
            flex-direction: row;
            height: 67px;
            margin: 0;
        }

        @include breakpoint(small down) {
            margin: 0 auto;
        }
    }

    &__category {
        @include font(var(--typo-1), 1.4rem, var(--fw-normal));
        color: var(--color-1--1);
        letter-spacing: 2.52px;
        margin: 0 0 15px;
        text-transform: uppercase;

        @include breakpoint(small down) {
            text-align: center;
        }
    }

    &__title {
        @include font(var(--typo-1), 2.4rem, var(--fw-bold));
        color: $color-black;
        display: flex;
        flex-direction: column;
        line-height: calc(28 / 24);
        margin: 0 0 20px;

        @include breakpoint(medium down) {
            font-size: 2.2rem;
            line-height: calc(24 / 22);
            margin: 0 0 25px;
        }

        @include breakpoint(small down) {
            font-size: 2rem;
            line-height: calc(24 / 20);
        }
    }

    &__title-link {
        @extend %link-block;
        @extend %underline-context;
    }

    &__teaser {
        @include font(var(--typo-1), 1.7rem, var(--fw-normal));
        color: $color-black;
        line-height: calc(26 / 17);
        margin: 0 0 17px;

        @include breakpoint(medium down) {
            font-size: 1.6rem;
            line-height: calc(22 / 16);
        }

        @include breakpoint(small down) {
            font-size: 1.4rem;
            line-height: calc(18 / 14);
        }
    }
}
