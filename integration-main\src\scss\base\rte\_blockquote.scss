// @name Blockquote

.blockquote,
blockquote {
    @include font(var(--typo-1), 2.8rem, var(--fw-normal));
    @include decor-mask('quote', var(--color-2--1), 78px);
    color: $color-3--5;
    line-height: 1.55;
    margin: 70px 0;
    padding: 5px 0 5px 156px;
    position: relative;

    @include breakpoint(medium down) {
        font-size: 2rem;
    }

    @include breakpoint(small down) {
        padding: 72px 0 0 0;
        text-align: center;
    }

    &::after {
        @include absolute(0, null, null, 120px);
        @include size(1px, 100%);
        background-color: $color-3--3;
        content: '';

        @include breakpoint(small down) {
            content: none;
        }
    }

    &::before {
        @include absolute(-14px, null, null, 0);

        @include breakpoint(small down) {
            left: 50%;
            transform: translateX(-50%);
        }
    }

    p:not([class]) {
        @extend %font-inherit;
        line-height: inherit;
        margin: 0 0 20px;

        &:last-child {
            margin-bottom: 0;
        }
    }

    a:not([data-fancybox]) {
        color: inherit;
        padding: 0 3px;

        @include on-event {
            background-color: transparent;
            color: inherit;
            text-decoration: none;
        }
    }
}

.blockquote-secondary {
    margin: 70px 0;

    figcaption {
        color: $color-3--4;
        font-size: 2rem;
        padding: 0 0 0 156px;
        position: relative;

        @include breakpoint(small down) {
            font-size: 1.6rem;
            padding: 0;
            text-align: center;
        }

        &::before {
            @include absolute(14px, null, 14px, 126px);
            @include size(24px, 1px);
            background-color: $color-3--4;
            content: '';

            @include breakpoint(small down) {
                left: 50%;
                top: -5px;
                transform: translateX(-50%);
            }
        }
    }

    blockquote {
        margin: 0 0 20px;
    }
}
