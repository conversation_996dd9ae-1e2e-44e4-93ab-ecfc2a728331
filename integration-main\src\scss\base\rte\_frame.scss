.frame {
    $this: &;
    margin: 80px 0;
    @include breakpoint(small down) {
        margin: 40px 0;
    }

    #{$this}__title {
        @include font(var(--typo-1), 1.8rem, var(--fw-bold));
        color: $color-3--3;
        margin: 1.75em 0;
    }

    &__wrapper {
        position: relative;
        width: 100%;
    }

    &__ratio {
        @include size(100%, auto);
        display: block;
    }

    &__link {
        @include icon-before($fa-var-download);
        color: $color-black;
        display: block;
        font-size: 1.8rem;
        margin-top: 15px;

        &::before {
            color: var(--color-1--2);
            font-size: 1.4rem;
            margin-right: 0;
        }

        @include breakpoint(small down) {
            font-size: 1.6rem;
        }
    }

    iframe,
    object,
    embed {
        @include absolute(0, null, null, 0);
        @include size(100%);
        border: 0;
    }
}
