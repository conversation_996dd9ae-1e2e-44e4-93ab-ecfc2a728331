%rte-heading {
    color: $color-black;
    font-family: var(--typo-1);
    font-weight: var(--fw-bold);
    line-height: 1.25;

    a {
        color: inherit;

        &:hover,
        &:focus {
            color: $color-black;
        }
    }
}

@mixin rte-h2() {
    @extend %rte-heading;
    @include font(null, 4.5rem, var(--fw-bold));
    color: var(--color-1--1);
    margin: 100px 0 30px;
    overflow: hidden;

    @include breakpoint(medium down) {
        font-size: 3.8rem;
    }

    @include breakpoint(small down) {
        font-size: 2.8rem;
        margin: 50px 0 15px;
    }
}

@mixin rte-h3() {
    @extend %rte-heading;
    @include font(null, 4rem, var(--fw-bold));
    color: var(--color-1--4);
    margin: 70px 0 30px;

    @include breakpoint(medium down) {
        font-size: 3.4rem;
    }

    @include breakpoint(small down) {
        font-size: 2.6rem;
        margin: 40px 0 15px;
    }
}

// @name Title h4
@mixin rte-h4() {
    @extend %rte-heading;
    @include font(null, 3.2rem, var(--fw-bold));
    color: var(--color-1--2);
    margin: 60px 0 20px;

    @include breakpoint(medium down) {
        font-size: 2.6rem;
    }

    @include breakpoint(small down) {
        font-size: 2.3rem;
        margin: 35px 0 10px;
    }
}

@mixin rte-h5() {
    @extend %rte-heading;
    @include font(null, 2.4rem, var(--fw-normal));
    margin: 50px 0 20px;

    @include breakpoint(medium down) {
        font-size: 2rem;
    }

    @include breakpoint(small down) {
        margin: 50px 0 20px;
    }
}

@mixin rte-h6() {
    @extend %rte-heading;
    color: $color-3--4;
    font-size: 2rem;
    margin: 20px 0 10px;

    @include breakpoint(small down) {
        font-size: 1.6rem;
        margin: 10px 0 5px;
    }
}
