// @name Table
.table-responsive {
    display: block;
    margin: 50px 0;
    max-width: 1080px;
    overflow-x: auto;
    /* stylelint-disable */
    -webkit-overflow-scrolling: touch;
    //-ms-overflow-style: -ms-autohiding-scrollbar;
    /* stylelint-enable */
    width: 100%;

    // Uncomment this code if you need to add styles to scroll bar
    //&::-webkit-scrollbar {
    //    appearance: none;
    //    background-color: rgba($color-3--1, 0.5);
    //    width: 10px;
    //}

    //&::-webkit-scrollbar-thumb {
    //    background-color: var(--color-1--1);
    //}
}

table {
    @include font(var(--typo-1), 1.6rem, var(--fw-normal), normal);
    background-color: transparent;
    border-collapse: collapse;
    border-spacing: 0;
    color: $color-black;
    line-height: 1.18;
    max-width: 100%;
    width: 100%;

    caption {
        @include font(var(--typo-1), 2.2rem, var(--fw-normal), normal);
        color: $color-3--4;
        margin-bottom: 20px;
        text-align: left;

        @include breakpoint(small down) {
            font-size: 2rem;
        }
    }

    th,
    td {
        @include trs;
        border-right: 1px solid $color-white;
        padding: 18px 20px 16px 13px;

        ul,
        p {
            font-size: 1.4rem;
        }
    }

    th {
        @include font(var(--typo-1), 1.6rem, var(--fw-bold));
        background-color: var(--color-1--1);
        color: $color-white;
        font-stretch: condensed;
        text-align: left;
    }

    td {
        background-color: $color-3--2;
        border-top: 1px solid $color-white;
        min-width: 140px;

        &:empty {
            background-color: transparent;
        }
    }

    tbody tr:nth-child(2n) td {
        background-color: $color-3--1;
    }

    // Uncomment this line if you need hover
    //tbody tr td:hover {
    //    background-color: $color-3--4;
    //    color: $color-white;
    //}
}
