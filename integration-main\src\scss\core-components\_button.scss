//  Button settings.
$btn-min-width: 100px;
$btn-height-large: 80px;
$btn-height-default: 63px;
$btn-height-small: 45px;

// Disabled button styles
@mixin btn-disabled-style {
    &.is-disabled,
    &[disabled],
    &[aria-disabled='true'] {
        @content;
    }

    // Only for links
    @at-root {
        a#{&}.is-disabled,
        a#{&}[aria-disabled='true'] {
            pointer-events: none;

            @include on-event {
                box-shadow: none;
                outline: 0;
            }
        }
    }
}

// Only icon button styles
@mixin btn-only-icon-style {
    @include size($btn-height-default);
    min-width: 0;
    padding: 0;

    @include fa-icon-style(false) {
        margin: 0;
    }

    > * + *:not(.ghost) {
        margin-left: 0;
    }

    svg {
        margin: 0;
    }

    &.is-small {
        @include size($btn-height-small);
        min-height: $btn-height-small;
    }

    &.is-large {
        @include size($btn-height-large);
        min-height: $btn-height-large;
    }
}

$default-padding: (1.7em 3.3em);
$small-padding: (1.1em 2.4em);
$large-padding: (2.1em 4.9em);

@mixin button-size-small {
    font-size: 1.2rem;
    min-height: $btn-height-small;
    padding: $small-padding;

    @include fa-icon-style(false) {
        margin-top: 0;
    }

    span:not([class*='fa-']) {
        letter-spacing: 1.2px;
    }
}

@mixin button-size-sm-default {
    @include breakpoint(medium) {
        font-size: 1.2rem;
        min-height: $btn-height-large;
        padding: $large-padding;
    }
}

@mixin button-size-large {
    font-size: 1.4rem;
    min-height: $btn-height-large;
    padding: $large-padding;

    @include breakpoint(medium down) {
        font-size: 1.3rem;
        min-height: $btn-height-default;
        padding: $default-padding;
    }

    @include breakpoint(small down) {
        font-size: 1.2rem;
        min-height: $btn-height-small;
        padding: $small-padding;
    }

    span:not([class*='fa-']) {
        letter-spacing: 1.4px;
    }
}

//
// Basic styles for buttons.
//
// https://habr.com/ru/company/ruvds/blog/489820/
// https://ishadeed.com/article/styling-the-good-old-button/
%button {
    @include trs;
    @include focus-outline;
    @include font(var(--typo-1), 1.3rem, var(--fw-normal), normal);
    @include min-size($btn-min-width, $btn-height-default);
    align-items: center;
    appearance: none;
    background-color: transparent;
    border: 1px solid var(--color-1--1);
    color: var(--color-1--1);
    cursor: pointer;
    display: inline-flex;
    justify-content: center;
    line-height: 1.2;
    margin: 0;
    padding: $default-padding;
    position: relative;
    text-decoration: none;
    text-transform: uppercase;

    @include breakpoint(small down) {
        font-size: 1.2rem;
        min-height: $btn-height-small;
        padding: $small-padding;
    }

    > * + *:not(.ghost) {
        margin-left: 8px;
    }

    @include fa-icon-style(false) {
        @include font(null, 1em, var(--fw-normal));
        color: inherit;
        margin-top: -1px; // If it needs
        pointer-events: none;

        &::before {
            color: inherit;
            line-height: 1.2;
        }
    }

    span:not([class*='fa-']) {
        letter-spacing: 1.3px;
    }

    span.btn__number {
        @include trs;
        color: var(--color-1--2);
        margin-left: 17px;
        position: relative;
        z-index: 2;

        &::after {
            @include absolute(50%, null, null, 50%);
            @include size(27px);
            @include trs;
            background-color: var(--color-2--1);
            border-radius: 50%;
            content: '';
            transform: translateX(-50%) translateY(-50%);
            z-index: -1;
        }

        &.is-hidden {
            display: none;
        }
    }

    span.btn__svg {
        @include size(1.1em, 1em);

        svg {
            @include size(100%);
            fill: currentColor;
        }
    }

    svg {
        @include size(1em);
        color: inherit;
        fill: currentColor;
    }

    svg,
    span {
        pointer-events: none;
    }

    @include on-event {
        background-color: var(--color-1--1);
        border-color: var(--color-1--1);
        color: $color-white;

        span.btn__number {
            color: var(--color-2--1);

            &::after {
                background-color: var(--color-1--2);
            }
        }
    }

    @include btn-disabled-style {
        cursor: not-allowed;
        opacity: 0.5;

        @include on-event {
            background-color: transparent;
            border-color: var(--color-1--1);
            color: var(--color-1--1);
        }
    }
}

// Small button styles, only size
%button-size-small {
    @include button-size-small;
}

%button-size-md-small {
    @include breakpoint(medium down) {
        @include button-size-small;
    }
}

%button-size-sm-small {
    @include breakpoint(small down) {
        @include button-size-small;
    }
}

// Large button small on mobile, only size
%button-size-sm-default {
    @include button-size-sm-default;
}

// Large button styles, only size
%button-size-large {
    @include button-size-large;
}

%button-size-md-large {
    @include breakpoint(medium down) {
        @include button-size-large;
    }
}

%button-size-sm-large {
    @include breakpoint(small down) {
        @include button-size-large;
    }
}

// Styles for buttons with icon only
%button-style-only-icon {
    @include btn-only-icon-style;
}

// Styles for rounded buttons, only border-radius.
%button-style-rounded {
    border-radius: 5px;
}

// Styles for only icon circle buttons.
%button-style-circle {
    border-radius: 50%;
}

// Styles for main buttons, only colors
%button-style-basic {
    border-color: var(--color-1--2);
    color: var(--color-1--2);

    @include on-event {
        background-color: var(--color-1--2);
        border-color: var(--color-1--2);
        color: $color-white;
    }

    @include btn-disabled-style {
        background-color: transparent;
        border-color: var(--color-1--2);
        color: var(--color-1--2);
    }
}

// Styles for primary buttons, only colors
%button-style-primary {
    background-color: var(--color-1--1);
    border-color: var(--color-1--1);
    color: $color-white;
    font-weight: var(--fw-bold);

    @include on-event {
        background-color: var(--color-1--4);
        border-color: var(--color-1--4);
    }

    @include btn-disabled-style {
        @include on-event {
            background-color: var(--color-1--1);
            border-color: var(--color-1--1);
            color: $color-white;
        }
    }
}

// Styles for secondary buttons, only colors
%button-style-secondary {
    background-color: var(--color-1--2);
    border-color: var(--color-1--2);
    color: $color-white;
    font-weight: var(--fw-bold);

    @include on-event {
        background-color: var(--color-1--1);
        border-color: var(--color-1--1);
    }

    @include btn-disabled-style {
        @include on-event {
            background-color: var(--color-1--2);
            border-color: var(--color-1--2);
            color: $color-white;
        }
    }
}

%button-style-ternary {
    background-color: var(--color-2--1);
    border-color: var(--color-2--2);
    color: var(--color-1--2);
    font-weight: var(--fw-bold);

    @include on-event {
        background-color: var(--color-2--2);
        border-color: var(--color-2--2);
        color: $color-white;
    }

    @include btn-disabled-style {
        @include on-event {
            background-color: var(--color-2--1);
            border-color: var(--color-2--2);
            color: var(--color-1--2);
        }
    }
}

// Styles for link buttons, colors and text-decoration
%button-style-link {
    background-color: transparent;
    border-color: transparent;
    color: var(--color-1--2);
    font-weight: 700;
    min-height: auto;
    padding: 5px;

    @include fa-icon-style(false) {
        @include trs;
        color: var(--color-1--2);
    }

    > svg {
        fill: var(--color-1--2);
    }

    @include on-event {
        background-color: transparent;
        border-color: transparent;
        color: var(--color-1--1);

        @include fa-icon-style(false) {
            color: var(--color-1--1);
        }

        > *:not(svg):not([class*='fa-']) {
            text-decoration: underline;
        }
    }

    @include btn-disabled-style {
        @include on-event {
            background-color: transparent;
            border-color: transparent;

            > * {
                text-decoration: none !important;
            }
        }
    }
}

// Inverted styles, only colors
%button-style-inverted {
    background-color: transparent !important;
    border-color: $color-white !important;
    color: $color-white !important;

    @include on-event {
        background-color: var(--color-2--3) !important;
        border-color: var(--color-2--3) !important;
        color: var(--color-1--2) !important;
    }

    @include btn-disabled-style {
        @include on-event {
            background-color: transparent !important;
            border-color: $color-white !important;
            color: $color-white !important;
        }
    }
}

// Inverted hover styles for primary, secondary, ternary buttons
%button-style-inverted-hover {
    @include on-event {
        background-color: var(--color-1--3);
        border-color: var(--color-1--3);
        color: var(--color-1--2);
    }
}

// Hide button text on from tablet devices
%button-hide-text-on-tablet {
    @include breakpoint(medium down) {
        @include hide-text;
        @include btn-only-icon-style;

        @include fa-icon-style(false) {
            text-indent: 0;
        }
    }
}

// Hide button text on from mobile devices
%button-hide-text-on-mobile {
    @include breakpoint(small down) {
        @include hide-text;
        @include btn-only-icon-style;

        @include fa-icon-style(false) {
            text-indent: 0;
        }
    }
}

//$btn-min-width: 100px;
//$btn-height-large: 70px;
//$btn-height-default: 52px;
//$btn-height-small: 40px;

%button-animated {
    min-width: $btn-height-default;
    padding-left: 1.25em;
    padding-right: 1.25em;

    span:not([class*='fa-']) {
        max-width: 0;
        overflow: hidden;
        transition-duration: 300ms;
        transition-property: margin-left, max-width;
        white-space: nowrap;
    }

    span + span {
        margin-left: 0;
        transition-duration: 300ms;
        transition-property: margin-left, max-width;
    }

    @include on-event {
        span:not([class*='fa-']) {
            max-width: 300px;
        }

        span + span {
            margin-left: 5px;
        }
    }

    &.is-small {
        min-width: $btn-height-small;
        padding-left: 1.025em;
        padding-right: 1.025em;
    }

    &.is-large {
        min-width: $btn-height-large;
        padding-left: 1.31em;
        padding-right: 1.31em;
    }
}

.btn {
    @extend %button;

    &.is-full-width {
        width: 100%;
    }

    &.is-rounded {
        @extend %button-style-rounded;
    }

    &.is-circle {
        @extend %button-style-circle;
    }

    &.is-only-icon {
        @extend %button-style-only-icon;
    }

    &.is-link {
        @extend %button-style-link;
    }

    &.is-basic {
        @extend %button-style-basic;
    }

    &.is-primary {
        @extend %button-style-primary;
    }

    &.is-secondary {
        @extend %button-style-secondary;
    }

    &.is-ternary {
        @extend %button-style-ternary;
    }

    &.is-sm-default {
        @extend %button-size-sm-default;
    }

    &.is-large {
        @extend %button-size-large;
    }

    &.is-md-large {
        @extend %button-size-md-large;
    }

    &.is-sm-large {
        @extend %button-size-sm-large;
    }

    &.is-small {
        @extend %button-size-small;
    }

    &.is-md-small {
        @extend %button-size-md-small;
    }

    &.is-sm-small {
        @extend %button-size-sm-small;
    }

    &.is-md-text-hidden {
        @extend %button-hide-text-on-tablet;
    }

    &.is-sm-text-hidden {
        @extend %button-hide-text-on-mobile;
    }

    &.is-more {
        min-width: 262px;
    }

    &.is-animated {
        @extend %button-animated;
    }

    &.is-inverted-hover {
        @extend %button-style-inverted-hover;
    }

    @include add-inverted-styles {
        @extend %button-style-inverted;
    }

    &.js-choice-reset {
        display: none;
        margin-top: 1rem;

        &.is-visible {
            display: block;
        }
    }

    &.is-hidden {
        display: none;
    }
}

/**
 * Buttons/Links group component.
 */
%button-links-group {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    margin: 0 -5px;

    > button,
    > a,
    > .btn {
        margin: 5px;
    }

    &.is-center {
        justify-content: center;
    }

    &.is-left {
        justify-content: flex-start;
    }

    &.is-right {
        justify-content: flex-end;
    }
}

.links-group,
.buttons-group {
    @extend %button-links-group;
}

.link-wordpress {
    margin: 30px 0;
}
