/*
 * Click-roll component.
 */

.click-and-roll {
    $this: &;

    border-top: 2px solid transparent;
    margin: 40px 0;
    position: relative;

    @include breakpoint(small down) {
        margin: 20px 0;
    }

    & + & {
        margin-top: -40px;

        @include breakpoint(small down) {
            margin-top: -20px;
        }

        #{$this} {
            #{$this}__toggle {
                padding: 0 5px;
            }

            #{$this}__toggle-icon {
                @include min-size(50px);
                padding: 20px 15px;
            }

            #{$this}__toggle-text {
                font-size: 2rem;
            }
        }
    }

    .vertical-bar & {
        margin: 30px 0 0;
    }

    &__toggle-wrapper {
        margin: 0 !important;
    }

    &__toggle {
        @include trs;
        @include font(var(--typo-1), 2.2rem, var(--fw-normal));
        align-items: center;
        background-color: $color-3--1;
        border: 0;
        color: var(--color-1--1);
        cursor: pointer;
        display: flex;
        flex-direction: row-reverse;
        min-height: 67px;
        padding: 0 5px 0 35px;
        position: relative;
        text-align: left;
        width: 100%;

        @include breakpoint(small down) {
            padding: 0 5px 0 20px;
        }

        &:hover,
        &:focus,
        .is-open > & {
            background-color: var(--color-1--1);
            color: $color-white;

            #{$this}__toggle-icon {
                &::before {
                    color: $color-white;
                }
            }
        }
    }

    &__toggle-icon {
        @include icon-before($fa-var-angle-down);
        @include min-size(60px);
        align-items: center;
        align-self: flex-start;
        display: flex;
        justify-content: center;
        padding: 22px 20px;

        &::before {
            @include trs;
            color: $color-black;
            font-size: 2.2rem;
            font-weight: var(--fw-normal);
            line-height: 1;
        }
    }

    &__toggle-text {
        @include font(var(--typo-1), 2.2rem, var(--fw-normal));
        color: inherit;
        flex-grow: 1;
        line-height: 1.3;
        padding: 20px 5px;
    }

    &__block {
        @include font(null, 2rem, var(--fw-normal), null);
        display: none;
        line-height: (33*0.05);
        overflow: hidden;
        z-index: 5;
    }

    &__wrapper {
        border-left: 2px solid var(--color-1--1);
        margin: 40px 0 70px;
        padding: 0 40px;

        @include breakpoint(small down) {
            margin-bottom: 50px;
            padding: 0 20px;
        }

        .rte {
            > *:first-child {
                margin-top: 0;
            }

            > *:last-child {
                margin-bottom: 0;
            }

            li,
            p:not([class]) {
                a {
                    color: $color-black;
                    text-decoration: underline;

                    @include on-event {
                        background-color: transparent;
                        color: $color-black;
                        text-decoration: none;
                    }
                }
            }
        }

        .list__item {
            &:last-child {
                margin-bottom: 0 !important;
            }
        }
    }

    &.is-open {
        > #{$this}__toggle-wrapper > #{$this}__toggle {
            background-color: var(--color-1--1);
            color: $color-white;

            > #{$this}__toggle-icon {
                &::before {
                    color: $color-white;
                    transform: scaleY(-1);
                }
            }
        }

        > #{$this}__block {
            display: block;
        }
    }
}
