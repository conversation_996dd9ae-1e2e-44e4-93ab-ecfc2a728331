.js-autocomplete {
    position: relative;

    &.is-visible {
        background-color: $color-white;
    }

    .loader-grow {
        @include absolute(null, 50px, 10px);
    }
}

.js-autocomplete-input {
    padding-right: var(--global-autocomplete-offset);
}

.js-autocomplete-result-wrapper {
    @include trs;
    @include absolute(80px, 0, null, 0);
    @include font(
        var(--global-input-ff),
        var(--global-input-fz),
        var(--global-input-fw),
        var(--global-input-fs)
    );
    align-items: flex-end;
    background-color: $color-white;
    border: 1px solid var(--global-input-border-color);
    border-radius: var(--global-input-border-radius);
    box-shadow: var(--global-form-dropdown-shadow);
    display: flex;
    flex-direction: column;
    margin-top: 5px;
    opacity: 0;
    overflow: hidden;
    visibility: hidden;
    z-index: 11;

    .is-visible & {
        opacity: 1;
        visibility: visible;
    }
}

.js-autocomplete-result-list {
    @extend %clear-list-styles;
    @extend %font-inherit;
    border-bottom: 1px solid var(--global-input-border-color);
    width: 100%;
}

.rte .js-autocomplete-result-text,
.js-autocomplete-result-link,
.js-autocomplete-result-text {
    @include trs;
    cursor: pointer;
    display: flex;
    font-size: inherit;
    line-height: initial;
    margin: 0;
    padding: var(--global-input-padding);
    text-decoration: none;

    li[aria-selected='true'] &,
    &:focus,
    &:hover {
        background-color: rgba($color-black, 0.1);
    }

    span {
        color: $color-black;
        pointer-events: none;
        width: 100%;

        + span {
            flex-shrink: 0;
            text-align: right;
            width: 35%;
        }
    }
}

.js-autocomplete-input-clear {
    @extend %button;
    @extend %button-style-circle;
    @extend %button-style-secondary;
    @include trs;
    @include absolute(null, 18px, 10px, null);
    @include min-size(30px);
    line-height: 1;
    opacity: 0;
    padding: 3px;
    text-align: center;
    visibility: hidden;
    z-index: 5;

    &.is-visible {
        opacity: 1;
        visibility: visible;
    }
}

.js-autocomplete-input-clear-icon {
    @include absolute(50%, null, null, 50%);
    @include icon-before($fa-var-times);
    transform: translate(-45%, -45%);

    &::before {
        font-size: 1.5rem;
        font-weight: var(--fw-normal);
    }
}

.js-autocomplete-input-clear-text {
    @extend %visually-hidden;
}
