/*
 * Base styles for legend component.
 */

.legend,
.form-title,
.rte .legend,
legend {
    @include font(
        var(--global-legend-ff),
        var(--global-legend-fz-desktop),
        var(--global-legend-fw),
        var(--global-legend-fs)
    );
    color: var(--global-legend-color);
    display: block;
    padding: 0;
    text-align: left;
    white-space: normal;
    width: 100%;

    @include breakpoint(medium down) {
        font-size: var(--global-legend-fz-tablet);
    }

    @include breakpoint(small down) {
        font-size: var(--global-legend-fz-mobile);
    }

    &.is-background {
        background-color: var(--color-1--1);
        color: $color-white;
        font-size: 1.4rem;
        font-weight: var(--fw-normal);
        letter-spacing: 2.52px;
        margin: 25px 0;
        padding: 8px 18px;
        text-transform: uppercase;
    }
}

.required:not(input):not(textarea):not(select) {
    @include font(
        var(--global-label-required-ff),
        var(--global-label-required-fz),
        var(--global-label-required-fw),
        var(--global-label-required-fs)
    );
    color: var(--global-label-required-color);
    text-transform: capitalize;
}

.text-help {
    --text-color: var(--global-label-helper-color);

    @include font(
        var(--global-label-helper-ff),
        var(--global-label-helper-fz),
        var(--global-label-helper-fw),
        var(--global-label-helper-fs)
    );
    color: var(--text-color);
    display: block;
    margin: 5px 0;
    text-transform: none;

    &.is-alert {
        --text-color: var(--global-label-alert-color);
    }

    &.is-tooltip {
        @include fadeOut();
        @include font(var(--typo-1), 1.2rem, var(--fw-normal));
        @include absolute(0, null, null, 0);
        background-color: $color-3--4;
        color: $color-white;
        padding: 0.6em 1.2em;
        transform: translateY(-75%);
        z-index: -1;

        &::before {
            @include absolute(null, null, 2px, 50%);
            border-left: 7px solid transparent;
            border-right: 7px solid transparent;
            border-top: 7px solid $color-3--4;
            content: '';
            transform: translate(-50%, 8px);
        }
    }

    a {
        text-decoration: underline;

        @include on-event {
            text-decoration: none;
        }
    }
}

/**
 * Base styles for label component.
 */

%label {
    @include font(
        var(--global-label-ff),
        var(--global-label-fz),
        var(--global-label-fw),
        var(--global-label-fs)
    );
    color: var(--global-label-color);
    cursor: pointer;
    display: block;
    line-height: 1.25;
    position: relative;

    .required {
        font-size: inherit;
        margin: 0 4px;
    }

    &.is-inline {
        > * {
            display: inline-block;
        }
    }

    a {
        text-decoration: underline;

        @include on-event {
            text-decoration: none;
        }
    }
}

.label,
.rte .label,
label {
    @extend %label;

    &__description {
        @include font(null, 1.8rem, var(--fw-normal));
        display: block;
        margin-top: 10px;
    }

    &__frequency {
        @include font(null, 1.2rem, var(--fw-normal));
        color: $color-3--4;
        display: block;
        line-height: 1;
        margin-top: 10px;
        padding-top: 10px;
        position: relative;
        text-transform: uppercase;

        &::before {
            @include absolute(0, null, null, 0);
            @include size(55px, 1px);
            background-color: $color-3--3;
            content: '';
        }
    }
}

// @name text label
// @description style label when label is used in wrapper
.text-label {
    display: block;
    margin: 0 0 5px;
}
