// Gallery
.gallery {
    $this: &;

    display: block;
    margin: 0 0 70px;
    position: relative;

    @include breakpoint(medium down) {
        margin: 0 0 50px;
    }

    &__title {
        @include font(var(--typo-1), 2.2rem, var(--fw-normal), normal);
        color: $color-3--4;
        margin-bottom: 20px;

        @include breakpoint(small down) {
            font-size: 2rem;
        }
    }
}

.gallery-info {
    @include absolute(null, 0, 0);
    @include font(var(--typo-1), 1.2rem, var(--fw-bold), normal);
    background-color: var(--color-2--1);
    border: 1px solid var(--color-2--2);
    color: var(--color-1--2);
    display: none;
    letter-spacing: 1.2px;
    min-width: 45px;
    padding: 13px 5px;
    text-align: center;
    z-index: 25;

    @include breakpoint(small down) {
        display: inline-block;
    }

    &__text {
        display: none;

        .has-blocked-elements & {
            display: inline-block;
        }
    }
}

.gallery-list {
    $this: &;

    display: flex;
    flex-wrap: wrap;
    position: relative;

    &__item {
        animation: 0.4s fadeInUp cubic-bezier(0.25, 0.25, 0.25, 1.25) both;
        display: flex;
        flex-direction: column;
        margin: 0;
        width: calc(100% / 3);

        @include breakpoint(small down) {
            display: none;
            width: 100%;

            // If we have only 1 item, show it
            &:only-child {
                display: block;
            }

            // If we have not any links only placeholder, show the first
            .no-links &:first-child {
                display: block;
            }

            // If we have links, display all of them
            &.is-link {
                display: block;

                // And hide all instead first
                & ~ & {
                    display: none;
                }
            }
        }
    }
}

.gallery-item {
    $this: &;

    @include size(100%);
    display: block;
    overflow: hidden;
    position: relative;

    &::before {
        @include absolute(0, null, null, 0);
        @include size(100%);
        @include trs;
        content: "";
        opacity: 0;
        pointer-events: none;

        @include breakpoint(small down) {
            content: none;
        }
    }

    &:focus-visible {
        &::before {
            outline: 2px solid $color-white;
            outline-offset: -4px;
        }
    }

    @include on-event {
        &::before {
            background-color: rgba($color-black, 0.7);
            opacity: 1;
            z-index: 1;
        }

        @include fa-icon-style(false) {
            animation: 0.3s pulse linear 1 forwards;
        }

        &.is-video {
            &::after {
                content: none;
            }
        }

        picture[data-error-alt]::before {
            color: $color-white;
        }

        #{$this}__picture {
            img {
                transform: scale(1.1);
            }
        }

        #{$this}__caption {
            opacity: 1;
        }
    }

    &__group {
        display: block;
        margin: 0;
        position: relative;
        width: 100%;
    }

    &__figure {
        @include size(100%);
    }

    // need for priority
    & &__picture {
        @include size(100%);
        display: block;
        position: relative;

        img {
            @include object-fit;
            @include trs;
            @include size(100%);
            display: block;
        }
    }

    &__caption {
        @include absolute(0, null, null, 0);
        @include size(100%);
        @include trs();
        display: flex;
        flex-direction: column;
        justify-content: center;
        margin: 0;
        opacity: 0;
        padding: 20px;
        z-index: 2;

        @include breakpoint(small down) {
            display: none;
        }
    }

    &__text {
        @include font(var(--typo-1), 1.8rem, var(--fw-normal), normal);
        -moz-box-orient: vertical; // for Firefox
        -webkit-box-orient: vertical; // for Chrome, Opera, Safari, Edge
        color: $color-white;
        display: -webkit-box; // for Chrome, Opera, Safari, Edge
        display: -moz-box; // for Firefox
        -moz-line-clamp: 3; // add 3 buttons if text content get more than 3 lines for Firefox
        -webkit-line-clamp: 3; // add 3 buttons if text content get more than 3 lines for Chrome, Opera, Safari, Edge
        line-height: 1.2;
        max-height: 66px;

        @include breakpoint(medium down) {
            font-size: 1.6rem;
            max-height: 60px;
        }
    }

    &__copyright {
        @include font(var(--typo-1), 1.6rem, var(--fw-normal), normal);
        color: $color-white;
        margin-top: 12px;
        max-height: 21px;
        overflow: hidden;
        text-shadow: 0 0 6px rgba(0, 0, 0, 0.16);

        @include breakpoint(medium down) {
            font-size: 1.4rem;
            margin-top: 8px;
        }
    }

    @include fa-icon-style(false) {
        @include trs;
        @include font(null, 4rem, var(--fw-normal));
        align-items: center;
        background-color: transparent;
        color: $color-white;
        display: flex;
        justify-content: center;
        margin: 0 0 16px;
        z-index: 10;

        @include breakpoint(medium down) {
            font-size: 3.2rem;
            margin: 0 0 8px;
        }
    }

    &.is-video {
        @include icon-after($fa-var-play);

        &::after {
            @include absolute(50%, null, null, 50%);
            @include font(null, 3rem, var(--fw-normal));
            color: $color-white;
            transform: translate(-50%, -50%);
        }

        @include fa-icon-style(false) {
            animation: none;
            background-color: transparent;
        }

        @include on-event {
            @include fa-icon-style(false) {
                animation: 0.3s pulse linear 1 forwards;
            }
        }
    }

    &.cookies-denied {
        @include responsive-ratio(400, 267, "after");
        align-items: center;
        background-color: $color-3--5;
        border: 0;
        border-radius: 0;
        cursor: pointer;
        display: flex;
        font-family: var(--typo-1);
        font-size: 1.4rem;
        justify-content: center;
        padding: 0;

        .cookies-denied__message,
        button {
            @include breakpoint(medium only) {
                font-size: 1.2rem;
            }
        }

        span {
            background-color: $tarteaucitron-green;
            border-radius: 4px;
            color: $color-white;
            display: inline-block;
            font-weight: var(--fw-normal);
            line-height: 1.3;
            margin: 10px;
            max-width: calc(100% - 20px);
            padding: 5px;
            position: absolute;
            z-index: 10;
        }

        &.is-video {
            &::after {
                left: 0;
                position: relative;
                top: 0;
                transform: none;
            }

            @include on-event {
                &::after {
                    content: "";
                }
            }
        }
    }
}

.fancybox-thumbs {
    &__list {
        a[style*='youtube'],
        a[style*='vimeo'],
        a[style*='dailymotion'] {
            @include icon-after($fa-var-play);
            color: $color-white;
            font-size: 1.4rem;
            text-indent: -9999px;

            &::before,
            &::after {
                text-indent: initial;
            }

            &::after {
                @include abs-center;
                z-index: 4;
            }
        }

        a {
            &::before {
                border-color: $fb-focus-color;
            }

            &:focus {
                &::before {
                    opacity: 1;
                }
            }
        }
    }
}

.fancybox-button {
    &:focus-visible {
        outline: 2px solid currentColor;
        outline-offset: 1px;
    }
}

.fancybox-infobar {
    color: $color-white;
}
