/*
 * Pager component.
 */

.pager {
    $this: &;

    width: 100%;

    & &__listitems {
        @include font(var(--typo-1), 1.4rem, var(--fw-normal));
        display: flex;
        margin: 30px 0 80px;
        padding: 0;
        position: relative;
        text-transform: uppercase;
        width: 100%;

        &.is-hidden {
            display: none;
        }

        &.is-center {
            justify-content: center;
        }

        @include breakpoint(small down) {
            justify-content: center;
            padding-bottom: 20px;
        }
    }

    &__item {
        align-items: center;
        display: inline-flex;
        margin: 0 2.5px;

        #{$this}__link {
            @include trs;
            @include font(var(--typo-1), 1.2rem, var(--fw-normal));
            @include min-size(45px);
            align-items: center;
            border: 1px solid var(--color-1--2);
            color: var(--color-1--2);
            display: flex;
            height: 45px; // Need for IE
            justify-content: center;
            letter-spacing: 1.2px;
            padding: 0 0.5em;
            text-decoration: none;
        }

        #{$this}__text,
        #{$this}__icon {
            pointer-events: none;
        }

        &:not(.is-return) {
            a#{$this}__link {
                @include on-event {
                    background-color: var(--color-1--1);
                    border-color: var(--color-1--1);
                    color: $color-white;
                }
            }
        }

        span#{$this}__link {
            background-color: var(--color-2--1);
            border-color: var(--color-2--2);
            color: var(--color-1--2);
            cursor: auto;
        }

        &.is-prev,
        &.is-next {
            #{$this}__link {
                @include min-size(auto);
                height: auto;
                padding: 13px 29px;
            }
        }

        &.is-prev,
        &.is-next,
        &.is-return {
            span#{$this}__link {
                background-color: transparent;
                border-color: $color-3--4;
                color: $color-3--4;
                opacity: 0.7;
            }

            #{$this}__icon {
                @include icon-before($icon: '', $fw: var(--fw-normal));

                &::before {
                    color: inherit;
                }
            }
        }

        &.is-prev {
            flex-grow: 1;
            justify-content: flex-start;
            margin-right: 1rem;
            width: 1%;

            #{$this}__icon {
                margin-right: 8px;

                &::before {
                    content: fa-content($fa-var-arrow-left);
                }
            }

            #{$this}__description {
                margin-left: 15px;
            }

            @include breakpoint(small down) {
                @include absolute(100%, null, null, 0);
                max-width: 152px;
                width: auto;
            }
        }

        &.is-next {
            flex-grow: 1;
            justify-content: flex-end;
            margin-left: 1rem;
            width: 1%;

            #{$this}__icon {
                margin-left: 8px;

                &::before {
                    content: fa-content($fa-var-arrow-right);
                }
            }

            #{$this}__description {
                margin-right: 15px;
                text-align: right;
            }

            @include breakpoint(small down) {
                @include absolute(100%, 0, null, null);
                max-width: 152px;
                width: auto;
            }
        }

        &.is-return {
            flex-shrink: 0;
            z-index: 2;

            #{$this}__link {
                @include focus-outline($offset: 2px);
                border-color: transparent;
                color: var(--color-1--1);
                font-weight: var(--fw-bold);
                height: 20px;
                min-height: 20px;
                padding: 0 1em;

                @include on-event {
                    text-decoration: underline;
                }
            }

            #{$this}__icon {
                color: var(--color-1--1);
                margin-right: 8px;

                &::before {
                    content: fa-content($fa-var-list);
                }
            }
        }
    }

    &__separator,
    &__description {
        @include font(var(--typo-1), 1.4rem, var(--fw-normal));
        color: $color-3--4;
    }

    &__separator {
        @include min-size(45px);
        align-items: center;
        display: flex;
        font-weight: 500;
        justify-content: center;
    }

    &__description {
        font-style: italic;
        max-width: 230px;
        text-transform: none;

        @include breakpoint(medium down) {
            display: none;
        }
    }
}

.pager-infinite {
    margin: 20px 0 50px;
    text-align: center;

    &__button {
        @extend %button;
        @extend %button-style-link;
        color: var(--color-1--1);
        font-size: 1.2rem;

        @include fa-icon-style(false) {
            color: var(--color-1--1);
            margin-top: 0;
        }

        &.is-loading {
            @include fa-icon-style(false) {
                animation: spin 1s infinite linear;
            }
        }
    }
}

.pager-alphabet {
    $this: &;

    margin: 0 0 50px;

    @include breakpoint(medium down) {
        margin-bottom: 30px;
    }

    &__title {
        @include font(
            var(--global-legend-ff),
            var(--global-legend-fz-desktop),
            var(--global-legend-fw),
            var(--global-legend-fs)
        );
        color: var(--global-legend-color);
        margin-bottom: 40px;
        padding-right: 30px;

        @include breakpoint(medium down) {
            font-size: var(--global-legend-fz-tablet);
        }

        @include breakpoint(small down) {
            font-size: var(--global-legend-fz-mobile);
        }
    }

    & &__listitems {
        @extend %clear-list-styles;
        display: inline-flex;
        flex-wrap: wrap;
        margin: 0 -2px;
    }

    &__item {
        display: inline-flex;
        margin: 2px;

        #{$this}__link {
            @include trs;
            @include font(var(--typo-1), 1.2rem, var(--fw-normal));
            @include min-size(45px);
            align-items: center;
            border: 1px solid var(--color-1--2);
            color: var(--color-1--2);
            display: flex;
            height: 45px; // Need for IE
            justify-content: center;
            padding: 0 0.5em;
            text-decoration: none;

            @include breakpoint(small down) {
                @include min-size(30px);
                height: 30px; // Need for IE
            }
        }

        a#{$this}__link {
            &:hover,
            &:focus {
                background-color: var(--color-1--1);
                border-color: var(--color-1--1);
                color: $color-white;

                #{$this}__icon {
                    color: $color-white;
                }
            }
        }

        &.is-disabled {
            #{$this}__link {
                opacity: 0.5;
            }
        }

        &.is-current {
            #{$this}__link {
                background-color: var(--color-2--1);
                border-color: var(--color-2--2);
                color: var(--color-1--2);
                font-weight: var(--fw-bold);
                pointer-events: none;
            }
        }

        &.is-tous {
            #{$this}__link {
                padding: 0 2em;
                width: auto;
            }
        }

        &.is-number {
            #{$this}__link {
                width: 90px;
            }
        }
    }
}
