.password-rule {
    display: flex;
    flex-wrap: wrap;
    $this: &;

    &__item {
        @include font(var(--typo-1), 1.2rem, var(--fw-normal));
        color: $color-3--5;
        margin-right: 11px;

        &:last-child {
            margin-right: 0;
        }

        &.is-active {
            color: $color-active;

            #{$this}__icon {
                @include icon-before($fa-var-check);

                &::before {
                    color: $color-active;
                }
            }
        }

        &.is-error {
            color: $color-required;

            #{$this}__icon {
                &::before {
                    color: $color-required;
                }
            }
        }
    }

    &__icon {
        @include font(null, 1.3rem, var(--fw-normal));

        &::before {
            font-weight: var(--fw-normal);
            margin-right: 3px;
        }
    }
}
