/**
 * Number articles component.
 */

.number-articles {
    @include font(var(--typo-1), 2.2rem, var(--fw-normal));
    color: $color-3--4;
    text-align: right;

    @include breakpoint(medium down) {
        text-align: left;
    }

    @include breakpoint(small down) {
        font-size: 2rem;
        text-align: center;
    }

    @include add-inverted-styles {
        color: $color-white;
    }
}

.js-page-title {
    display: none;
}

/**
 * Status component.
 */

.status {
    @include font(var(--typo-1), 1.6rem, 400, normal);
    color: $color-black;
    display: inline-block;
    line-height: 1;
    padding: 0.55em 0.7em;
    text-align: center;

    @include fa-icon-style {
        color: currentColor;
        font-weight: var(--fw-normal);
        line-height: 1;
        margin-right: 5px;
    }

    &.is-new {
        background-color: var(--color-2--1);
        color: var(--color-1--2);
    }

    &.is-in-progress {
        background-color: var(--color-1--1);
        color: $color-white;
    }

    &.is-assign {
        background-color: var(--color-1--3);
        color: var(--color-1--2);
    }

    &.is-survey {
        background-color: $color-3--3;
    }

    &.is-new-poll {
        background-color: var(--color-1--1);
        color: $color-white;
    }
}

/**
 * Base styles for price component.
 */
.price {
    @include font(var(--typo-1), 1.8rem, var(--fw-normal));
    color: $color-3--4;
}

/**
 * Time-place component.
 */
.time-place {
    $this: &;

    &__item {
        @include font(var(--typo-1), 1.1rem, var(--fw-normal), normal);
        color: $color-3--4;
        display: flex;
        flex-wrap: nowrap;
        line-height: 1.14;
        margin: 4px 0;
        position: relative;

        @include fa-icon-style(false) {
            margin-right: 5px;
        }

        &.is-time {
            time + span {
                margin: 0 5px;
            }
        }

        &.is-place {
            line-height: 1.4;

            @include fa-icon-style(false) {
                margin: 1px 5px 0 2px;
            }
        }
    }

    &.is-center {
        #{$this}__item {
            justify-content: center;
            text-align: center;
        }
    }

    &.is-inline {
        #{$this}__item {
            display: inline-flex;
            margin: 3px 22px 3px 0;
            vertical-align: baseline;

            &:last-child {
                margin-right: 0;
            }
        }
    }

    &.is-primary {
        align-items: center;
        background-color: var(--color-1--1);
        display: flex;
        padding: 8px 20px;
        width: 226px;

        #{$this}__item {
            color: $color-white;
        }
    }
}

/**
 * Base styles for date component.
 */
.date {
    $this: &;

    @include font(var(--typo-1), 1.8rem, var(--fw-bold));
    @include min-size(220px, 70px);
    @include line-decor();
    align-items: center;
    background-color: var(--color-1--1);
    color: $color-white;
    display: inline-flex;
    flex-grow: 1;
    line-height: 1;
    padding: 12px 27px;
    position: relative;

    @include breakpoint(medium down) {
        @include min-size(169px, 67px);
        font-size: 1.4rem;
        padding: 12px 23px;
    }

    @include breakpoint(small down) {
        @include min-size(220px, 67px);
    }

    &::before {
        @include absolute(null, null, 0, 27px);
        transform: translateY(50%);

        @include breakpoint(medium down) {
            left: 23px;
        }
    }

    &__time {
        line-height: 1.2;

        &:first-of-type {
            display: block;
        }
    }

    &__icon {
        @include icon-before($fa-var-long-arrow-right);
        font-size: 1.8rem;
        font-weight: 400;
        margin-right: 10px;

        @include breakpoint(medium down) {
            font-size: 1.6rem;
        }
    }

    &.is-large {
        @include min-size(338px, 111px);
        font-size: 2.8rem;
        padding: 20px 40px;

        @include breakpoint(medium down) {
            @include min-size(292px, 100px);
            font-size: 2.2rem;
        }

        @include breakpoint(small down) {
            @include min-size(264px, 92px);
            padding: 19px 33px;
        }

        &::before {
            @include size(100px, 17px);
            bottom: auto;
            left: 40px;
            top: 0;
            transform: translateY(-50%);

            @include breakpoint(small down) {
                left: 33px;
            }
        }

        #{$this}__icon {
            font-size: 2.8rem;
            margin-right: 8px;

            @include breakpoint(medium down) {
                font-size: 2.2rem;
            }
        }
    }

    &.is-primary {
        align-items: flex-start;
        background-color: var(--color-2--1);
        color: var(--color-1--2);
        flex-grow: 0;
        font-size: 1.4rem;
        min-height: 67px;
        padding: 12px 17px;
        width: 226px;

        &::before {
            content: none;
        }

        #{$this}__wrap {
            margin-top: 5px;
        }

        #{$this}__icon {
            font-size: 1.6rem;
            margin-right: 7px;
        }

        #{$this}__svg-wrap {
            @include size(40px);
            display: block;
            flex-shrink: 0;
            margin-right: 10px;

            svg {
                @include size(100%);
                fill: var(--color-1--2);
            }
        }
    }
}

/**
 * Base styles for loader component.
 */

$loader-sizes: (
    small: 20px,
    medium: 40px,
    large: 50px
);

@keyframes loader-rotate {
    to {
        transform: rotate(360deg);
    }
}

@keyframes loader-grow {
    0% {
        transform: scale(0);
    }

    50% {
        opacity: 1;
    }
}

.loader-spinner {
    @include size(30px);
    color: $color-black;
    display: inline-block;
    position: relative;
    vertical-align: text-bottom;

    &::before {
        @include size(100%);
        @include absolute(0, null, null, 0);
        border: 2.5px solid currentColor;
        border-radius: 50%;
        border-right-color: transparent;
        content: '';
    }

    &.is-active {
        &::before {
            animation: loader-rotate 0.75s linear infinite;
        }
    }
}

.loader-grow {
    @include size(30px);
    color: $color-black;
    display: inline-block;
    position: relative;
    vertical-align: text-bottom;

    &::before {
        @include size(100%);
        @include absolute(0, null, null, 0);
        background-color: currentColor;
        border-radius: 50%;
        content: '';
        opacity: 0;
    }

    &.is-active {
        &::before {
            animation: loader-grow 0.75s linear infinite;
        }
    }
}

.loader-spinner,
.loader-grow {
    @each $class, $size in $loader-sizes {
        &.is-#{$class} {
            @include size($size);
        }
    }
}

/**
 * Deadline component.
 */
.deadline {
    @include font(var(--typo-1), 2.4rem, var(--fw-bold));
    color: $color-black;

    &__content {
        flex-grow: 1;
        width: 1%;
    }

    &.is-small {
        font-size: 1.8rem;
        font-weight: 500;

        @include breakpoint(small down) {
            font-size: 1.6rem;
        }
    }

    &.is-new {
        color: var(--color-1--1);
    }
}

/**
 * List document component.
 */
.document-actions {
    $this: &;

    @at-root {
        ul#{&} {
            align-items: flex-start;
            display: flex;
            flex-direction: column;
            margin: 0;
        }
    }

    &__item {
        padding: 2.5px 0;
    }

    &__link {
        @extend %button;
        @extend %button-size-small;
    }
}

/**
 * Tag links component.
 */
.tag-links {
    display: flex;
    flex-shrink: 0;
    flex-wrap: wrap;
    gap: 6px;
    max-width: 400px;

    @include breakpoint(medium down) {
        max-width: 345px;
    }

    @include breakpoint(small down) {
        justify-content: center;
        margin: 0;
    }
}

/**
 * Request details component.
 */
.request-details {
    display: flex;
    flex-wrap: wrap;

    @include breakpoint(small down) {
        flex-direction: column;
    }

    &__item {
        align-items: center;
        display: flex;
        justify-content: center;
        min-height: 67px;
        padding: 21px 35px;

        @include breakpoint(small down) {
            min-height: 50px;
            padding: 10px;
        }

        @include fa-icon-style(false) {
            margin-right: 10px;
        }

        time {
            font-weight: var(--fw-bold);
        }

        &.is-number {
            @include font(null, 1.8rem, var(--fw-normal));
            background-color: var(--color-2--1);
            color: var(--color-1--2);
        }

        &.is-status {
            @include font(null, 1.8rem, var(--fw-bold));
            background-color: #0465b4;
            color: $color-white;
        }

        &.is-date {
            @include font(null, 1.2rem, var(--fw-normal));
            background-color: $color-3--1;
            color: $color-3--4;
            flex-grow: 1;
            justify-content: flex-start;
            text-transform: uppercase;

            @include breakpoint(medium only) {
                flex-grow: initial;
                min-width: 432px;
            }

            @include breakpoint(small down) {
                justify-content: center;
            }
        }
    }
}

.lds-ripple {
    @include size(19px);
    display: inline-block;
    position: relative;

    div {
        animation: lds-ripple 1s cubic-bezier(0, 0.2, 0.8, 1) infinite;
        border: 4px solid #fff;
        border-radius: 50%;
        opacity: 1;
        position: absolute;
    }

    div:nth-child(2) {
        animation-delay: -0.5s;
    }

    @keyframes lds-ripple {
        0% {
            @include size(0);
            left: 7.5px;
            opacity: 0;
            top: 7.5px;
        }

        4.9% {
            @include size(0);
            left: 7.5px;
            opacity: 0;
            top: 7.5px;
        }

        5% {
            @include size(0);
            left: 7.5px;
            opacity: 1;
            top: 7.5px;
        }

        100% {
            @include size(19px);
            left: 0;
            opacity: 0;
            top: 0;
        }
    }
}
