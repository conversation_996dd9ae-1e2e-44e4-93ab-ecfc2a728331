.table-responsive {
    display: block;
    margin: 0 0 70px;
    overflow-x: auto;
    /* stylelint-disable */
    -webkit-overflow-scrolling: touch;
    -ms-overflow-style: -ms-autohiding-scrollbar;
    /* stylelint-enable */
    width: 100%;

    &::-webkit-scrollbar-thumb {
        margin-top: 10px;
    }

    //&::-webkit-scrollbar {
    //    appearance: none;
    //    background-color: rgba($color-3--1, 0.5);
    //    width: 10px;
    //}
    //
    //&::-webkit-scrollbar-thumb {
    //    background-color: var(--color-1--1);
    //}

    &.is-completed {
        thead {
            th,
            td {
                background-color: $color-3--5;
            }
        }
    }
}

table {
    @include font(var(--typo-1), 1.4rem, var(--fw-normal), normal);
    background-color: transparent;
    border-collapse: collapse;
    border-spacing: 0;
    color: $color-black;
    line-height: 1.18;
    max-width: 100%;
    width: 100%;

    caption {
        @include font(var(--typo-1), 2.8rem, var(--fw-bold), normal);
        color: var(--color-1--1);
        margin-bottom: 30px;
        text-align: left;

        @include breakpoint(medium down) {
            font-size: 2.6rem;
        }

        @include breakpoint(small down) {
            font-size: 2.4rem;
        }

        h2 {
            @include font(null, inherit, inherit);
        }
    }

    th,
    td {
        @include trs;
        border-right: 1px solid $color-white;
        padding: 10px 20px;

        ul,
        p {
            font-size: 1.4rem;
        }
    }

    th {
        @include font(null, 1.4rem, var(--fw-bold));
        font-stretch: condensed;
        text-align: left;
    }

    td {
        background-color: $color-3--1;
        border-top: 1px solid $color-white;

        a {
            @include on-event() {
                text-decoration: none;
            }
        }
    }

    thead {
        th,
        td {
            background-color: var(--color-1--1);
            color: $color-white;
            font-weight: var(--fw-bold);

            &:empty {
                background-color: transparent;
            }
        }

        td {
            border-top: 0;
        }
    }

    tbody {
        th {
            background-color: rgba($color-3--2, 0.5);
            border-top: 1px solid $color-white;
            color: $color-black;
            padding-left: 30px;
            padding-right: 40px;
            text-align: right;
        }

        td {
            &:first-child:empty {
                background-color: transparent;
            }
        }
    }

    tr {
        th:last-child,
        td:last-child {
            border-right: 0;
        }
    }

    // Uncomment this line if you need hover
    //tbody tr td:hover {
    //    background-color: $color-3--4;
    //    color: $color-white;
    //}
}
