.tabs {
    $this: &;

    &__list {
        &[class] {
            align-items: flex-end;
            border-bottom: 1px solid $color-3--3;
            display: flex;
            flex-shrink: 0;
            margin: 0;
            padding: 0 20px;
        }

        &.is-center {
            justify-content: center;
        }

        &.is-right {
            justify-content: flex-end;
        }
    }

    &__list-item {
        justify-content: flex-start;
        margin: 0 3px -1px 0;
        max-width: 220px;
    }

    &__list-toggle,
    &__accordion-toggle {
        @include font(var(--typo-1), 1.6rem, var(--fw-normal), null);
        @include trs;
        @include size(100%);
        background-color: $color-3--1;
        border: 1px solid $color-3--3;
        color: $color-3--4;
        cursor: pointer;
        line-height: 1.18;
        margin: 0;
        padding: 23px 21px;
        position: relative;
        text-align: center;

        &::before {
            @include absolute(-1px, -1px, auto, -1px);
            @include size(calc(100% + 2px), 5px);
            @include trs;
            background-color: transparent;
            content: '';
        }

        @include fa-icon-style(false) {
            pointer-events: none;
        }

        &.is-current,
        &:hover,
        &:focus {
            background-color: $color-white;
            border-color: transparent $color-3--3;
            color: var(--color-1--1);

            &::before {
                background-color: var(--color-2--1);
            }
        }
    }

    &__panel {
        @include focus-outline();
        display: none;
        flex-grow: 1;
        padding: 30px 0;

        @include breakpoint(medium down) {
            padding: 20px;
        }

        &.is-current {
            display: block;
        }
    }

    &.is-vertical {
        display: flex;

        #{$this}__list {
            border-bottom: none;
            border-right: 1px solid #c5c5c5;
            display: block;
            height: 100%;
            padding: 20px 0;
        }

        #{$this}__list-item {
            margin: 0 -1px 3px 0;
            max-width: 384px;

            @include breakpoint(medium down) {
                max-width: 236px;
            }
        }

        #{$this}__list-toggle,
        #{$this}__accordion-toggle {
            text-align: left;

            &::before {
                @include size(5px, calc(100% + 2px));
            }

            &.is-current,
            &:hover,
            &:focus {
                border-color: $color-3--3 transparent;
            }
        }

        #{$this}__panel {
            border-top: none;
            padding: 20px 0 0 26px;
        }
    }

    &.is-minimalistic {
        #{$this}__list-item {
            margin: 0;
        }

        #{$this}__list-toggle,
        #{$this}__accordion-toggle {
            background-color: transparent;
            border-color: transparent;

            &::before {
                bottom: -3px;
                left: auto;
                right: -1px;
                top: auto;
            }

            &.is-current,
            &:hover,
            &:focus {
                border-color: transparent;
            }
        }
    }

    &.is-minimalistic.is-vertical {
        #{$this}__list-toggle,
        #{$this}__accordion-toggle {
            padding: 18px 21px;

            &::before {
                bottom: -1px;
                right: -3px;
            }
        }
    }

    &.is-with-accordion {
        @include breakpoint(small down) {
            display: block;
        }

        > #{$this}__list {
            @include breakpoint(small down) {
                display: none;
            }
        }

        #{$this}__accordion-toggle-wrap {
            display: none;
            margin: 0;

            @include breakpoint(small down) {
                display: block;
            }
        }

        #{$this}__accordion-toggle {
            @include breakpoint(small down) {
                border-color: transparent;
            }
        }

        #{$this}__panel {
            @include breakpoint(small down) {
                border: 1px solid $color-3--3;
                display: block;
                margin-top: 3px;
                padding: 0;
            }
        }

        #{$this}__content {
            display: none;

            @include breakpoint(small down) {
                padding: 15px;
            }

            &.is-current {
                display: block;
            }
        }
    }

    &.js-accordion-focus {
        box-shadow: 0 0 6px 0 #1e90ff;
        outline: 1px solid rgba(#1e90ff, 0.5);
        outline-offset: 0;
    }
}
