.tag {
    $this: &;
    @include font(var(--typo-1), 1.3rem, var(--fw-normal));
    @include trs;
    align-items: center;
    background-color: var(--color-1--1);
    border: 1px solid var(--color-1--1);
    color: $color-white;
    display: inline-flex;
    line-height: 1;
    padding: 0.6em 1em;
    text-decoration: none;

    @include on-event {
        background-color: var(--color-2--1);
        border-color: var(--color-2--1);
        color: var(--color-1--2);
    }

    &.is-light {
        background-color: $color-white;
        color: var(--color-1--1);

        @include on-event {
            background-color: var(--color-1--1);
            border-color: var(--color-1--1);
            color: $color-white;
        }
    }
}
