.theme {
    @include font(var(--typo-1), 1.4rem, var(--fw-normal));
    color: var(--color-1--1);
    letter-spacing: 2.52px;
    text-transform: uppercase;

    @include breakpoint(small down) {
        font-size: 1.2rem;
        letter-spacing: 2.16px;
    }

    @at-root {
        span#{&} {
            display: block;
        }
    }

    &.is-large {
        font-size: 1.8rem;
        letter-spacing: 3.24px;

        @include breakpoint(small down) {
            font-size: 1.4rem;
            letter-spacing: 2.52px;
        }
    }

    &.is-secondary {
        letter-spacing: normal;
        line-height: 1.3;
        text-transform: none;

        .is-inverted & {
            color: var(--color-2--1);
            font-size: 1.2rem;
        }
    }

    .is-inverted & {
        color: $color-white;
        text-shadow: 0 0 6px rgba($color-black, 0.16);
    }
}
