.title:not(.rte) {
    $this: '.title';

    @include font(var(--typo-1), 4.5rem, var(--fw-bold));
    color: $color-black;
    line-height: 1.25;

    @include breakpoint(medium down) {
        font-size: 3.5rem;
    }

    @include breakpoint(small down) {
        font-size: 2.8rem;
    }

    #{$this}__content {
        // Uncomment this line if you need svg as background image
        //@include decor-mask('albums', var(--color-1--1), 45px);
        align-items: center;
        display: flex;
        font-size: inherit;

        @include breakpoint(small down) {
            display: block;
            text-align: center;
            word-break: break-word;
        }
    }

    #{$this}__text {
        font-size: 1em;
    }

    #{$this}__svg {
        @include size(1em);
        display: block;
        flex-shrink: 0;
        margin-right: 0.25em;
        z-index: 2;

        @include breakpoint(small down) {
            display: inline-block;
        }

        svg {
            @include size(100%);
            display: block;
            fill: $color-black;
        }
    }

    &.is-primary {
        font-size: 7.5rem;
        max-width: 70%;
        position: relative;

        @include breakpoint(medium down) {
            font-size: 5.5rem;
            max-width: initial;
        }

        @include breakpoint(small down) {
            font-size: 3.8rem;
            max-width: 100%;
        }

        #{$this}__text {
            @include title-decor($style: primary);
            padding-bottom: 0;

            &::before {
                left: -40px;
            }
        }
    }

    &.is-secondary {
        @include font(var(--typo-1), 2.4rem, var(--fw-bold));
        padding-bottom: 0;

        @include breakpoint(medium down) {
            font-size: 2.2rem;
        }

        #{$this}__text {
            @include title-decor($style: center);
            color: var(--color-1--1);
            padding-bottom: 12px;
            text-transform: uppercase;
        }

        #{$this}__svg {
            @include size(1.25em);
            fill: var(--color-1--1);
            margin-bottom: 12px;
        }
    }

    &.is-tertiary {
        @include font(var(--typo-1), 3rem, var(--fw-bold));

        @include breakpoint(medium down) {
            font-size: 2.4rem;
        }

        #{$this}__text {
            text-transform: uppercase;
        }

        #{$this}__svg {
            @include size(2em);
        }
    }

    &.as-legend {
        @include font(var(--typo-1), 2.8rem, var(--fw-bold));
        color: var(--color-1--1);

        @include breakpoint(medium down) {
            font-size: 2.6rem;
        }

        @include breakpoint(small down) {
            font-size: 2.4rem;
        }

        #{$this}__content {
            @include breakpoint(small down) {
                text-align: left;
            }
        }
    }

    @at-root {
        #{$this}.is-hidden {
            display: none;
        }

        #{$this}.is-visible {
            display: block;
        }

        #{$this}.is-center {
            &::before {
                left: 50%;
                transform: translateX(-50%);
            }

            #{$this}__content {
                justify-content: center;

                @include breakpoint(small down) {
                    display: block;
                    text-align: center;
                }
            }
        }
    }

    @include add-inverted-styles {
        color: $color-white;

        #{$this}__text {
            color: $color-white;
        }

        svg {
            fill: $color-white;
        }
    }
}
