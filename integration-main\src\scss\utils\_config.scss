// Default development type.
$type: 'development' !default;

// Enable revenge css styles.
$enable-revenge: true !default;

// Path to the root folder, for SF you can use.
$path-root: '../' !default;

// Global domain variable.
$domain: 'stratis.fr' !default;

// Default assets paths.
$assets-images: '#{$path-root}images' !default;
$assets-icons: '#{$path-root}icons' !default;
$assets-fonts: '#{$path-root}fonts' !default;
$assets-project-icons: '#{$path-root}fonts/project-icons/' !default;
// FontAwesome icons config.
$fa-font-path: $assets-fonts + '/Fontawesome';

// Overwrite variables for CMS type.
@if ($type == 'typo3') {
    // Overwrite assets.
    $assets-images: '#{$path-root}Images';
    $assets-icons: '#{$path-root}Icons';
    $assets-fonts: '#{$path-root}Fonts';
    $assets-project-icons: '#{$path-root}Fonts/project-icons/';
    $fa-font-path: '#{$path-root}Fonts/Fontawesome/';

    // Disable revenge.
    $enable-revenge: false;
}

// Project breakpoints.
$breakpoints: (
    xsmall: 0,
    small: 480px,
    medium: 768px,
    large: 1280px,
) !default;

// Project breakpoints abbreviations
$breakpointsAbbreviations: (
    xsmall: 'xs',
    small: 'sm',
    medium: 'md',
    large: 'lg',
) !default;

// Breakpoints directions
$breakpointCoverages: 'only', 'down', 'up';

// Site colors.
// color-1
$color-1--1: #144d29 !default; // Color 1 from palette in design
$color-1--2: #091f2c !default; // Color 3 from palette in design
$color-1--3: #b9cabf !default; // color-1--1 at 30% transparency on white
$color-1--4: #0e361d !default; // color-1--1 at 70% transparency on black
$color-1--5: #4fa800 !default;

// color-2
$color-2--1: #00a6de !default; // Color 2 from palette in design
$color-2--2: #00749b !default; // color-2--1 at 70% transparency on black
$color-2--3: #b3e4f5 !default; // color-2--1 at 30% transparency on white

// Grey color
$color-3--1: #f7f7f7 !default;
$color-3--2: #efefef !default;
$color-3--3: #c5c5c5 !default;
$color-3--4: #707070 !default;
$color-3--5: #5a5a5a !default;

// Neutral colors
$color-black: #000;
$color-white: #fff;
$color-blue: #3593df;
$color-red: #f95b5c;
$color-yellow: #b78405;
$color-green: #5ca465;

// Tarteaucitron colors
$tarteaucitron-green: #1b870b;
$tarteaucitron-accept: #226e3a;
$tarteaucitron-refuse: #c81c1e;
$tarteaucitron-personalize: #5a5a5a;

// Helper colors
$color-default: #f1f6fb;
$color-danger: #fff5f5;
$color-warning: #fff3cd;
$color-success: #ecfcee;
$color-secondary: #f6f6f6;
$color-light: #fff;
$color-dark: #c5c5c5;

// Labels and messages colors
$color-required: $tarteaucitron-refuse;
$color-active: $tarteaucitron-accept;

// FranceConnect colors
$fc-link-color: #000091;
$fc-hover-color: #1212ff;

// Fancybox colors
$fb-focus-color : #df3f3f;

// Map
$ol-blue-color : #003c88;

// true - set white background color for filter fields.
// false - set default background color for filter fields.
$use-inverted-filter: true;

// The problem it that - CSS variables should be a static values
// That's why we should use it with SCSS variables.
// Need to notice that we should avoid such constructions rgba($color-1--1, 0.5)
// Try to use opacity instead.
:root {
    // General settings
    // Site fonts.
    // http://melchoyce.github.io/fontstacks/
    --typo-1: 'Fira Sans', Arial, Tahoma, sans-serif;

    // Project icons font name.
    --project-icons-typo: 'project-icons';

    // FontAwesome font name.
    --font-awesome-typo: 'Font Awesome 6 Pro';
    --font-awesome-brands-typo: 'Font Awesome 6 Brands';

    // Use calculator for set formulas for variables
    // http://razorltd.github.io/sasscolourfunctioncalculator/
    // Primary color
    --color-1--1: #{$color-1--1}; // #4269e2
    --color-1--2: #{darken(saturate(adjust-hue($color-1--1, 60), 7.27), 8.63)}; // #152b51
    --color-1--3: #{lighten(desaturate(adjust-hue($color-1--1, -1), 44.94), 56.86)}; // #c7d2f6
    --color-1--4: #{darken(saturate($color-1--1, 0.06), 5.69)}; // #2e499e
    --color-1--5: #{lighten(saturate(adjust-hue($color-1--1, -50), 41.24), 13.92)};

    // Secondary color
    --color-2--1: #{$color-2--1}; // #ff8977
    --color-2--2: #{darken($color-2--1, 13.14)}; // #b26053
    --color-2--3: #{lighten(desaturate($color-2--1, 23.26), 39.61)}; // #ffdcd6

    // Font weight
    --fw-thin: 100;
    --fw-extralight: 200;
    --fw-light: 300;
    --fw-normal: 400;
    --fw-medium: 500;
    --fw-semibold: 600;
    --fw-bold: 700;
    --fw-extrabold: 800;
    --fw-black: 900;

    // Form global settings
    --global-legend-fz-desktop: 2.8rem;
    --global-legend-fz-tablet: 2.6rem;
    --global-legend-fz-mobile: 2.4rem;
    --global-legend-ff: var(--typo-1);
    --global-legend-fw: var(--fw-black);
    --global-legend-fs: normal;
    --global-legend-color: var(--color-1--1);

    // Form label
    --global-label-fz: 2rem;
    --global-label-ff: var(--typo-1);
    --global-label-fw: var(--fw-bold);
    --global-label-fs: normal;
    --global-label-color: #{$color-black};

    // Form label required
    --global-label-required-fz: 1.4rem;
    --global-label-required-ff: inherit;
    --global-label-required-fw: var(--fw-medium);
    --global-label-required-fs: normal;
    --global-label-required-color: #{$color-required};

    // Form label helper
    --global-label-helper-fz: 1.2rem;
    --global-label-helper-ff: var(--typo-1);
    --global-label-helper-fw: var(--fw-normal);
    --global-label-helper-fs: normal;
    --global-label-helper-color: #{$color-3--5};

    // Form label alert
    --global-label-alert-color: #{$color-required};

    // Form input
    $global-input-fz: 1.8rem;
    $global-input-fz-mobile: 1.6rem;

    --global-input-fz: #{$global-input-fz};
    --global-input-fz-mobile: #{$global-input-fz-mobile};
    --global-input-ff: var(--typo-1);
    --global-input-fw: var(--fw-normal);
    --global-input-fs: normal;
    --global-input-color: #{$color-3--4};
    --global-input-bg: #{$color-white};
    --global-input-border-color: #{$color-3--5};
    --global-input-border-radius: 0;
    --global-input-min-height: 63px;
    --global-input-min-height-mobile: 45px;
    --global-input-radio-checkbox-min-height: 25px;
    --global-input-padding: 0.9em 1.67em;
    --global-input-padding-mobile: 0.55em 1.15em;

    // Form input placeholder
    --global-input-ph-fz: var(--global-input-fz);
    --global-input-ph-ff: var(--global-input-ff);
    --global-input-ph-fw: var(--fw-normal);
    --global-input-ph-fs: normal;
    --global-input-ph-color: var(--global-input-color);

    // Form input readonly
    --global-input-readonly-bg: #{$color-3--1};

    // Form textarea
    --global-textarea-height: 100px;

    // Form input autocomplete
    --global-autocomplete-offset: #{em(4.5, strip-unit($global-input-fz))};

    // Form select
    // !!!Need to think about solution to change caret color in dynamic way
    // !!!And also need to take into attention about this in site factory generation
    --global-select-caret: #{inline-svg('<svg fill="#{$color-black}" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 256 512" width="18" height="18"><path d="M119.5 326.9L3.5 209.1c-4.7-4.7-4.7-12.3 0-17l7.1-7.1c4.7-4.7 12.3-4.7 17 0L128 287.3l100.4-102.2c4.7-4.7 12.3-4.7 17 0l7.1 7.1c4.7 4.7 4.7 12.3 0 17L136.5 327c-4.7 4.6-12.3 4.6-17-.1z"/></svg>')};
    --global-select-caret-offset: 32px;
    --global-select-caret-offset-mobile: 20px;
    --global-select-padding-right: #{em(5.5, strip-unit($global-input-fz))};

    // Form Choices
    --global-choices-multiple-padding-bottom: 0.45em;
    --global-choices-padding-mobile: 0.55em 1.15em;
    --global-choices-multiple-item-offset: 0.25em;

    // Form Multiselect/Autocomplete/Choices dropdown shadow
    --global-form-dropdown-shadow: 0 3px 5px #{rgba($color-black, 0.6)};

    // Form radio/checkbox
    --global-rch-fz: #{$global-input-fz};
    --global-rch-ff: var(--global-input-ff);
    --global-rch-fw: var(--fw-normal);
    --global-rch-fs: normal;
    --global-rch-color: #{$color-black};
    --global-rch-left-offset: 33px;

    // Form radio/checkbox ::before
    --global-rch-pseudo-box-size: 24px;
    --global-rch-pseudo-box-bg: #{$color-white};
    --global-rch-pseudo-box-border: #{$color-3--5};

    // Form radio/checkbox ::after
    --global-rch-pseudo-checkmark-width: 2px;
    --global-rch-pseudo-checkmark-color: #{$color-3--2};
    --global-rch-pseudo-checkmark-color-active: var(--color-1--1);

    // Form radio/checkbox :disabled
    --global-rch-pseudo-checkmark-color-grayscale: #{grayscale($color-1--1)};

    // Form legend for radio/checkbox group
    --global-rch-legend-ff: var(--typo-1);
    --global-rch-legend-fz: 1.8rem;
    --global-rch-legend-fw: var(--fw-bold);
    --global-rch-legend-fs: normal;
    --global-rch-legend-color: #{$color-3--4};

    // Default width of scroll bar if Javascript is disabled
    --scrollbar-width: 17px;

    // Default scroll padding
    --scroll-padding: 20% 0;
}
