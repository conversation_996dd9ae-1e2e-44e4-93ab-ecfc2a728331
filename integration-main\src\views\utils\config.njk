{#
    MAX 4 DOMAINS
    preconnect presets:
        - google fonts - 'https://fonts.gstatic.com'
        - OSM maps -
        - CDN -
#}
{#
    @param {string} title - Page meta title @required
    @param {string} description - Page meta description
    @param {string} template - template name
    @param {string} bundle - bundle name @required
    @param {string[]} headerLogo - [path, alt]
    @param {string[]} footerLogo - [path, alt]
    @param {boolean} hasFilter - add filter button for heading.
    @param {string[]} preloadFonts - fonts array.
    @param {string[]} preconnect - preconnect to resources before load.
    @param {string[]} prefetch - prefetch to resources before load.
    @param {string} mobileBarColor - color for mobile tab bar.
    @param {string} bodyClass - add class for body element
    @param {string[]} preloadCSS - preload custom css
#}

{% set config = {
    title: 'Template default title',
    description: 'Template default description',
    template: 'page',
    bundle: 'page',
    hasFilter: false,
    hasFacets: false,
    initLoadingController: false,
    useSidebarBeforeContent: false,
    useSidebarAfterContent: false,
    hasPreloader: true,
    preloadFonts: [
        Helpers.path.fonts + '/subset-FiraSans/subset-FiraSans-Regular.woff2',
        Helpers.path.fonts + '/subset-FiraSans/subset-FiraSans-Bold.woff2'
    ],
    preloadCSS: [],
    preconnect: [],
    prefetch: [],
    mobileBarColor: '#fff',
    bodyClass: '',
    styleguidePage: 'Atoms',
    styleguideNav: {
        'Main': 'styleguide-main.html',
        'Base': 'styleguide-base.html',
        'Widgets': 'styleguide-widgets.html',
        'Templates': 'styleguide-templates.html'
    },
    styleguideAnchors: {}
} %}
